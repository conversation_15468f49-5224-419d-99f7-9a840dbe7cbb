// Using logo elements declared in globals.js
let logoPlane = null;
let logoLight = null;

// Funkcja ładowania tekstury z buforowaniem
async function loadTextureWithCaching(texturePath, scene) {
    // Note: textureCache is already defined in bloodCells.js, so we'll just use the existing one
    // Use a local reference for convenience
    const textureCache = window.textureCache;

    // Sprawdź, czy tekstura jest już w buforze
    if (textureCache[texturePath]) {
        if (DEBUG_MODE) console.log(`Using cached texture: ${texturePath}`);
        return textureCache[texturePath];
    }

    // Wczytaj nową teksturę
    return new Promise((resolve, reject) => {
        try {
            const texture = new BABYLON.Texture(
                texturePath,
                scene,
                false, // noMipmap
                false, // invertY
                BABYLON.Texture.BILINEAR_SAMPLINGMODE,
                () => {
                    // Sukces - zapisz w buforze i zwróć
                    textureCache[texturePath] = texture;
                    if (DEBUG_MODE) console.log(`Texture loaded and cached: ${texturePath}`);
                    resolve(texture);
                },
                (error) => {
                    // Błąd
                    console.warn(`Failed to load texture ${texturePath}`, error);
                    reject(error);
                }
            );
        } catch (e) {
            reject(e);
        }
    });
}

/**
 * Tworzy główny mesh tunelu dla gry z optymalizacjami.
 * Usuwa poprzedni tunel, jego światła i logo, jeśli istnieją.
 * Generuje nową ścieżkę tunelu i geometrię na podstawie podanej liczby segmentów.
 * Stosuje teksturę i właściwości materiału.
 * Dodaje dekoracyjne światła punktowe wewnątrz tunelu.
 * Dodaje logo Hemolens na końcu tunelu.
 * Analizuje geometrię, aby utworzyć logiczne sekcje dla rozgrywki.
 * @param {number} segments - Liczba segmentów nowego tunelu (kontroluje długość).
 * @returns {Promise<BABYLON.Mesh | null>} Obietnica, która rozwiązuje się z utworzonym meshem tunelu lub null w przypadku niepowodzenia.
 */
async function createTunnel(segments) {
    if (DEBUG_MODE) console.log(`Creating tunnel (Level ${gameLevel}): ${segments} segments...`);

    // --- Cleanup Previous Tunnel & Logo --- // MODIFIED comment
    tunnelSections.length = 0; // Clear logical sections data
    if (tunnelMesh) {
        tunnelMesh.dispose();
        tunnelMesh = null;
        if (DEBUG_MODE) console.log("Disposed previous tunnel mesh.");
    }
    // Dispose old tunnel lights
    scene.lights.forEach(light => {
        if (light.name.startsWith("tunnelLight")) {
            light.dispose();
        }
    });

    // Clean up FFR-related resources (lights, glow layers)
    if (typeof window.cleanupFFRResources === 'function') {
        window.cleanupFFRResources(scene);
        if (DEBUG_MODE) console.log("Cleaned up FFR resources");
    } else {
        if (DEBUG_MODE) console.warn("cleanupFFRResources function not available");
    }

    if (DEBUG_MODE) console.log("Disposed previous tunnel lights.");

    // *** ADDED: Dispose previous logo and light ***
    try {
        // Make sure logoPlane is initialized before trying to access it
        const currentLogoPlane = logoPlane; // Get the current global reference
        if (currentLogoPlane) {
            currentLogoPlane.dispose();
            logoPlane = null; // Update the module-level variable
            if (DEBUG_MODE) console.log("Disposed previous logo plane.");
        }
        // Update local reference after disposal

        // Make sure logoLight is initialized before trying to access it
        const currentLogoLight = logoLight; // Get the current global reference
        if (currentLogoLight) {
            currentLogoLight.dispose();
            logoLight = null; // Update the module-level variable
            if (DEBUG_MODE) console.log("Disposed previous logo light.");
        }
    } catch (error) {
        console.error("Error disposing logo elements:", error);
        // Update global references first
        logoPlane = null;
        logoLight = null;
    }
    // *** END: Added Cleanup ***

    const segmentLength = Config.TUNNEL_SEGMENT_LENGTH;
    Config.TUNNEL_NARROW_POINTS = []; // Wyczyść poprzednie informacje o wąskich punktach

    // --- Generate Path and Radii ---
    if (typeof createTunnelPathWithNarrows !== 'function') {
        console.error("createTunnelPathWithNarrows function is not defined!");
        return null;
    }
    const {path, radii, narrowPoints} = createTunnelPathWithNarrows(segments, segmentLength); // Use parameter
    Config.TUNNEL_NARROW_POINTS = narrowPoints;
    if (DEBUG_MODE) console.log(`Generated tunnel path: ${path.length} points, ${radii.length} radii, ${narrowPoints.length} narrows.`);
    if (path.length < 2 || radii.length === 0 || path.length !== radii.length) { // Check path length > 1
        console.error(`Tunnel path/radii generation failed or resulted in mismatch/short path! Path: ${path.length}, Radii: ${radii.length}`);
        return null; // Cannot create tunnel without valid path/radii
    }
    // --- End Path Generation ---

    // --- Texture Loading ---
    let tunnelTexture = null;
    const texturePath1 = "./textures/others_0003_color_1k.jpg";
    const texturePath2 = "textures/others_0003_color_1k.jpg";
    const fallbackTextureUrl = "https://assets.babylonjs.com/environments/backgroundGround.png";

    try {
        tunnelTexture = new BABYLON.Texture(texturePath1, scene,
            () => {
                if (DEBUG_MODE) console.log(`Texture ${texturePath1} loaded.`);
            },
            () => { // Error 1
                if (DEBUG_MODE) console.warn(`Failed ${texturePath1}, trying ${texturePath2}...`);
                tunnelTexture = new BABYLON.Texture(texturePath2, scene,
                    () => {
                        if (DEBUG_MODE) console.log(`Texture ${texturePath2} loaded.`);
                    },
                    () => { // Error 2
                        console.warn(`Failed ${texturePath2}, using fallback CDN.`);
                        tunnelTexture = new BABYLON.Texture(fallbackTextureUrl, scene,
                            () => {
                                if (DEBUG_MODE) console.log("Fallback CDN texture loaded.");
                            },
                            () => {
                                console.error("FATAL: Failed fallback CDN texture.");
                                tunnelTexture = null;
                            } // Error 3
                        );
                    }
                );
            }
        );
    } catch (e) {
        console.error("Error during texture creation:", e);
        console.warn("Using fallback CDN texture due to initial error.");
        try {
            tunnelTexture = new BABYLON.Texture(fallbackTextureUrl, scene);
        } catch (fallbackError) {
            console.error("FATAL: Failed fallback texture creation:", fallbackError);
            tunnelTexture = null;
        }
    }

    if (tunnelTexture) {
        tunnelTexture.uScale = 5;
        tunnelTexture.vScale = 30;
        tunnelTexture.wrapU = BABYLON.Texture.WRAP_ADDRESSMODE;
        tunnelTexture.wrapV = BABYLON.Texture.WRAP_ADDRESSMODE;
    }
    // --- End Texture Loading ---

    // --- Create Unified Tunnel Mesh ---
    try {
        const newTunnelMesh = BABYLON.MeshBuilder.CreateTube("tunnel", {
            path: path,
            radiusFunction: (i) => {
                // Clamp index to be within the bounds of the radii array
                const index = Math.min(radii.length - 1, Math.max(0, i));
                return radii[index];
            },
            tessellation: 64,       // Adjust for performance vs quality
            cap: BABYLON.Mesh.NO_CAP,
            sideOrientation: BABYLON.Mesh.BACKSIDE,
            updatable: false
        }, scene);

        tunnelMesh = newTunnelMesh; // Update the module-level variable

        if (DEBUG_MODE) console.log("Unified tunnel mesh created.");

        // --- Tunnel Material ---
        // Using PBRMaterial for better texture support and realistic rendering
        const tunnelMaterial = new BABYLON.PBRMaterial("tunnelMaterial", scene);
        tunnelMaterial.backFaceCulling = false;

        if (tunnelTexture) {
            // Apply main color (albedo) texture
            tunnelMaterial.albedoTexture = tunnelTexture;
            tunnelMaterial.albedoTexture.hasAlpha = false;
            tunnelMaterial.albedoTexture.level = 0.9; // Ensure texture is clearly visible

            // Set base material properties
            tunnelMaterial.metallic = 0.5; // Semi-metallic surface
            tunnelMaterial.roughness = 0.8; // Fairly rough surface

            // Try to load normal map for better detail (using OpenGL format)
            try {
                const normalTexture = new BABYLON.Texture("./textures/others_0003_normal_opengl_1k.png", scene);
                tunnelMaterial.normalTexture = normalTexture;
                if (DEBUG_MODE) console.log("Applied OpenGL normal map to tunnel material");
            } catch (normalError) {
                console.warn("Could not apply normal map to tunnel:", normalError);
            }

            // Try to load height map for bump mapping
            try {
                const heightTexture = new BABYLON.Texture("./textures/others_0003_height_1k.png", scene);
                tunnelMaterial.bumpTexture = heightTexture;
                tunnelMaterial.bumpTexture.level = 0.8;
                if (DEBUG_MODE) console.log("Applied height map for bump mapping");
            } catch (heightError) {
                console.warn("Could not apply height map to tunnel:", heightError);
            }

            // Try to load roughness texture
            try {
                const roughnessTexture = new BABYLON.Texture("./textures/others_0003_roughness_1k.jpg", scene);
                tunnelMaterial.roughnessTexture = roughnessTexture;
                if (DEBUG_MODE) console.log("Applied roughness map to tunnel material");
            } catch (roughnessError) {
                console.warn("Could not apply roughness map to tunnel:", roughnessError);
            }

            // Try to load ambient occlusion texture
            try {
                const aoTexture = new BABYLON.Texture("./textures/others_0003_ao_1k.jpg", scene);
                tunnelMaterial.ambientTexture = aoTexture;
                if (DEBUG_MODE) console.log("Applied ambient occlusion map to tunnel material");
            } catch (aoError) {
                console.warn("Could not apply ambient occlusion map to tunnel:", aoError);
            }

            // Try to load subsurface scattering texture
            try {
                const subsurfaceTexture = new BABYLON.Texture("./textures/others_0003_subsurface_1k.jpg", scene);
                tunnelMaterial.subSurfaceTexture = subsurfaceTexture;
                tunnelMaterial.subSurfaceIntensity = 0.5; // Adjust as needed
                if (DEBUG_MODE) console.log("Applied subsurface scattering map to tunnel material");
            } catch (subsurfaceError) {
                console.warn("Could not apply subsurface scattering map to tunnel:", subsurfaceError);
            }

            if (DEBUG_MODE) console.log("Applied PBR textures to tunnel material.");
        } else {
            // Fallback colors if texture loading fails
            tunnelMaterial.albedoColor = new BABYLON.Color3(0.5, 0.1, 0.1); // Dark red fallback
            tunnelMaterial.emissiveColor = new BABYLON.Color3(0.2, 0.05, 0.05);
            console.warn("Tunnel texture failed, using fallback colors.");
        }

        // Additional material settings
        tunnelMaterial.emissiveIntensity = 0.05; // Bardzo subtelny blask

        tunnelMesh.material = tunnelMaterial;

        // Apply Fresnel effect
        if (tunnelMaterial instanceof BABYLON.StandardMaterial || tunnelMaterial instanceof BABYLON.PBRMaterial) {
            try {
                tunnelMaterial.emissiveFresnelParameters = new BABYLON.FresnelParameters();
                tunnelMaterial.emissiveFresnelParameters.bias = 0.2;
                tunnelMaterial.emissiveFresnelParameters.power = 1;
                tunnelMaterial.emissiveFresnelParameters.leftColor = BABYLON.Color3.White();
                tunnelMaterial.emissiveFresnelParameters.rightColor = new BABYLON.Color3(0.5, 0.2, 0.2);
                if (DEBUG_MODE) console.log("Applied Fresnel effect to tunnel material.");
            } catch (e) {
                console.error("Error applying Fresnel Parameters:", e);
            }
        }
        // --- End Tunnel Material ---

    } catch (error) {
        console.error("FATAL: Failed to create tunnel mesh:", error);
        tunnelMesh = null;
        return null; // Indicate failure
    }

    // --- Add Point Lights Inside Tunnel ---
    const lightsStep = 35; // Place lights less frequently
    const radiusScaleFactor = 4.0; // Must match the scale factor used in createTunnelPathWithNarrows
    const scaledStartRadius = Config.TUNNEL_DIAMETER_START * radiusScaleFactor;
    const narrowingThreshold = scaledStartRadius * 0.65; // Threshold based on scaled start radius

    for (let i = 0; i < path.length; i += lightsStep) {
        const lightPosition = path[i];
        const currentRadius = radii[Math.min(radii.length - 1, i)]; // Current scaled radius
        const isNarrowing = currentRadius < narrowingThreshold;

        const light = new BABYLON.PointLight("tunnelLight" + i, lightPosition, scene);
        light.diffuse = isNarrowing ? new BABYLON.Color3(0.8, 0.4, 0.4) : new BABYLON.Color3(0.7, 0.5, 0.5);
        light.range = currentRadius * 7; // Range based on scaled radius
        light.intensity = isNarrowing ? 0.4 : 0.25; // Zmniejszono intensywność
        light.intensity = Math.max(0.1, Math.min(light.intensity, 1.0)); // Clamp intensity
        light.shadowEnabled = false; // Performance
    }
    if (DEBUG_MODE) console.log(`Added ${Math.ceil(path.length / lightsStep)} point lights inside tunnel.`);

    // --- *** ADD HEMOLENS LOGO AT TUNNEL START (PLAYER'S END) *** ---
    const logoPosition = path[0]; // Position at the very start of the path
    const lookAtPosition = path[1]; // Position to orient the logo towards
    const logoDirection = lookAtPosition.subtract(logoPosition).normalize();
    const logoSize = 10.0; // Adjust size as needed

    // Load logo texture
    const logoTextureUrl = "./textures/hemolens_logo.jpg"; // MAKE SURE THIS FILE EXISTS
    let logoTexture = null;
    try {
        logoTexture = new BABYLON.Texture(logoTextureUrl, scene,
            () => {
                if (DEBUG_MODE) console.log("Hemolens logo texture loaded.");
            },
            () => {
                console.warn(`Failed to load ${logoTextureUrl}. Logo will be blank.`);
            }
        );
    } catch (e) {
        console.error("Error creating logo texture:", e);
    }


    // Create Plane for the logo
    const newLogoPlane = BABYLON.MeshBuilder.CreatePlane("hemolensLogo", {
        width: logoSize,
        height: logoSize * 0.5
    }, scene); // Adjust height aspect ratio
    logoPlane = newLogoPlane; // Update the module-level variable

    logoPlane.position = logoPosition.add(logoDirection.scale(-0.1)); // Place slightly *before* the exact start point

    // Orient the logo to face the player approach direction
    // *** REVISED ORIENTATION LOGIC ***
    try {
        // The direction the plane's front face should point (towards the player's approach)
        const facingDirection = logoDirection; // Points from path[0] towards path[1]

        // The direction the Look Quaternion function needs (it aligns the mesh's local -Z axis with this)
        // So, we provide the NEGATIVE of the direction we want the FRONT face (+Z) to point.
        const lookQuatDirection = facingDirection.negate();

        // Calculate 'up' vector robustly, relative to the look direction
        let logoUp = BABYLON.Axis.Y;
        if (Math.abs(BABYLON.Vector3.Dot(lookQuatDirection, logoUp)) > 0.98) { // Check if look direction is too close to World Y
            logoUp = BABYLON.Axis.X; // Use World X as up if look direction is vertical
        }

        // Set rotation using the calculated direction and up vector
        logoPlane.rotationQuaternion = BABYLON.Quaternion.FromLookDirectionLH(lookQuatDirection, logoUp);
        if (DEBUG_MODE) console.log("Logo oriented using FromLookDirectionLH.");

    } catch (e) {
        console.error("Error setting logo rotation with Quaternion:", e);
        // Fallback: Simple lookAt (might have roll issues)
        logoPlane.lookAt(lookAtPosition);
        if (DEBUG_MODE) console.warn("Logo oriented using fallback lookAt().");
    }
    // *** END REVISED ORIENTATION LOGIC ***


    // Create Material for the logo
    const logoMaterial = new BABYLON.StandardMaterial("logoMat", scene);
    if (logoTexture) {
        logoMaterial.diffuseTexture = logoTexture;
        logoMaterial.emissiveTexture = logoTexture; // Make texture glow
        logoMaterial.useAlphaFromDiffuseTexture = true; // Use transparency from PNG
    }
    logoMaterial.emissiveColor = new BABYLON.Color3(1, 1, 1); // Make it bright white emissive
    logoMaterial.specularColor = BABYLON.Color3.Black(); // No specular reflection
    logoMaterial.backFaceCulling = false; // Visible from both sides
    logoMaterial.disableLighting = true; // Not affected by scene lights, always bright
    logoPlane.material = logoMaterial;
    logoPlane.isPickable = false; // Not interactive

    // Add a strong Point Light near the logo
    const newLogoLight = new BABYLON.PointLight("logoEndPointLight", logoPosition.add(logoDirection.scale(-1)), scene); // Place slightly behind logo
    logoLight = newLogoLight; // Update the module-level variable
    logoLight.intensity = 2.5; // Very bright
    logoLight.diffuse = new BABYLON.Color3(1, 1, 0.95); // White-ish light
    logoLight.range = 25; // Affects a reasonable area around the end
    logoLight.shadowEnabled = false; // No shadows needed for this

    if (DEBUG_MODE) console.log("Added Hemolens logo and light at tunnel end.");
    // --- *** END HEMOLENS LOGO *** ---

    // --- Analyze Geometry for Gameplay Sections ---
    if (typeof analyzeAndCreateTunnelSections !== 'function') {
        console.error("analyzeAndCreateTunnelSections function is not defined!");
    } else {
        // Pass the scale factor used during radius generation
        analyzeAndCreateTunnelSections(path, radii, radiusScaleFactor);
    }
    // --- End Analysis ---

    // --- Apply FFR Coloring to Tunnel ---
    // Odkładamy kolorowanie FFR na później, aby nie blokować renderowania tunelu
    setTimeout(() => {
        if (typeof applyFFRColoring === 'function') {
            try {
                applyFFRColoring();
                if (DEBUG_MODE) console.log("Applied FFR coloring to tunnel");
            } catch (error) {
                console.error("Error applying FFR coloring:", error);
            }
        } else {
            console.warn("applyFFRColoring function is not defined - tunnel will not have FFR coloring");
        }
    }, 1000); // Opóźnienie 1 sekunda
    // --- End FFR Coloring ---

    if (DEBUG_MODE) console.log(`Finished creating tunnel for Level ${gameLevel}.`);
    return tunnelMesh; // Return the newly created mesh
}

/**
 * Generates the center path points and corresponding radii for the tunnel.
 * Incorporates randomly placed narrow sections.
 * @param {number} segments - The total number of points/segments for the tunnel path.
 * @param {number} [segmentLength=5] - The approximate length of each segment.
 * @returns {{path: BABYLON.Vector3[], radii: number[], narrowPoints: object[]}} An object containing the path points, radii array, and information about narrow points.
 */
function createTunnelPathWithNarrows(segments, segmentLength = 5) {
    const path = [];
    const radii = [];
    const narrowPointsInfo = [];
    let currentPos = new BABYLON.Vector3(0, 0, 0);
    let currentDir = new BABYLON.Vector3(0, 0, 1);
    const turnFactor = Config.TUNNEL_TURN_FACTOR;
    const upDownFactor = Config.TUNNEL_UPDOWN_FACTOR;
    const scaleFactor = 4.0; // MUST match usage elsewhere

    // --- Stricter Input Validation ---
    if (!segments || typeof segments !== 'number' || segments <= 10) { // Check type and value
        console.error(`Invalid segment count provided: ${segments}. Must be a number > 10.`);
        return {path: [], radii: [], narrowPoints: []};
    }
    // --- End Validation ---


    // --- Narrowing Configuration (Simplified & Robust) ---
    const numNarrows = 3 + Math.floor(Math.random() * Math.min(5, Math.floor(segments / 50))); // Limit narrows
    const narrowPositions = [];
    const safeStartMargin = Math.max(5, Math.floor(segments * 0.15)); // Don't place narrows too close to start
    const safeEndMargin = Math.max(5, Math.floor(segments * 0.15));   // Don't place narrows too close to end
    const availableRange = segments - safeStartMargin - safeEndMargin;

    if (numNarrows > 0 && availableRange > numNarrows * 5) { // Ensure enough space to distribute
        const step = availableRange / (numNarrows + 1); // Divide range into sections for narrows
        for (let i = 0; i < numNarrows; i++) {
            // Place narrow roughly in the middle of its section, with some randomness
            const basePos = safeStartMargin + (i + 1) * step;
            const randomOffset = (Math.random() - 0.5) * step * 0.6; // Randomness within section
            let finalPos = Math.floor(basePos + randomOffset);
            // *** Clamp position strictly away from ends ***
            finalPos = Math.max(safeStartMargin, Math.min(segments - 1 - safeEndMargin, finalPos));
            narrowPositions.push(finalPos);
        }
    } else if (numNarrows > 0) {
        // Fallback: Place one narrow roughly in the middle if range is too small
        let middlePos = Math.floor(segments / 2 + (Math.random() - 0.5) * 10);
        middlePos = Math.max(safeStartMargin, Math.min(segments - 1 - safeEndMargin, middlePos));
        narrowPositions.push(middlePos);
        if (DEBUG_MODE) console.warn("Tunnel too short for desired narrows, placing one near middle.");
    }
    // --- End Narrowing Config ---


    // Remove duplicates and sort
    const uniqueNarrowPositions = [...new Set(narrowPositions)];
    uniqueNarrowPositions.sort((a, b) => a - b);
    // *** Add Check: Ensure no narrows at 0 or segments-1 explicitly ***
    if (uniqueNarrowPositions[0] === 0) {
        uniqueNarrowPositions.shift(); // Remove if first element is 0
        if (DEBUG_MODE) console.warn("Removed narrow point at index 0.");
    }
    if (uniqueNarrowPositions.length > 0 && uniqueNarrowPositions[uniqueNarrowPositions.length - 1] === segments - 1) {
        uniqueNarrowPositions.pop(); // Remove if last element is segments-1
        if (DEBUG_MODE) console.warn("Removed narrow point at index segments-1.");
    }

    if (DEBUG_MODE) console.log(`Level ${gameLevel} Narrows final positions:`, uniqueNarrowPositions);

    // Radii definitions with variation
    // Add variation to the diameter (±15% of the average value)
    const variationFactor = 0.85 + (Math.random() * 0.3); // Random factor between 0.85 and 1.15
    const startRadiusUnscaled = Config.TUNNEL_DIAMETER_START * variationFactor;
    const endRadiusUnscaled = Config.TUNNEL_DIAMETER_END * 2.0 * variationFactor;
    const narrowRadiusUnscaled = Config.TUNNEL_DIAMETER_MIN * 2.0 * variationFactor;

    if (DEBUG_MODE) {
        console.log(`Tunnel diameter variation: factor=${variationFactor.toFixed(2)}`);
        console.log(`Actual diameter: ${(startRadiusUnscaled).toFixed(2)}mm (from avg ${Config.TUNNEL_DIAMETER_START}mm)`);
    }


    // --- Generate Path and Radii Points ---
    try { // Add try-catch around the loop for safety
        for (let i = 0; i < segments; i++) {
            path.push(currentPos.clone());

            let targetRadiusUnscaled;
            let isAtNarrowPeak = false;
            const narrowIndexMatch = uniqueNarrowPositions.indexOf(i);

            // Make the first segment have a larger diameter close to the base average
            if (i === 0 || i === 1) {
                // Use the base diameter for the first segment (both start and end points)
                targetRadiusUnscaled = startRadiusUnscaled;
            } else if (narrowIndexMatch !== -1) {
                targetRadiusUnscaled = narrowRadiusUnscaled;
                isAtNarrowPeak = true;
                narrowPointsInfo.push({
                    index: i,
                    position: currentPos.clone(),
                    radius: targetRadiusUnscaled * scaleFactor,
                    severity: (startRadiusUnscaled - targetRadiusUnscaled) / startRadiusUnscaled
                });
            } else if (i === segments - 1) {
                targetRadiusUnscaled = endRadiusUnscaled;
            } else {
                // *** Interpolation Logic with defensive checks ***
                let prevNarrowIdx = -1;
                let nextNarrowIdx = -1;
                for (let j = 0; j < uniqueNarrowPositions.length; j++) {
                    if (uniqueNarrowPositions[j] > i) {
                        nextNarrowIdx = j;
                        break;
                    }
                    prevNarrowIdx = j;
                }

                // Handle cases based on surrounding narrows
                if (uniqueNarrowPositions.length === 0) {
                    // No narrow points defined, simple lerp from start to end
                    const t = i / (segments - 1);
                    targetRadiusUnscaled = BABYLON.Scalar.Lerp(startRadiusUnscaled, endRadiusUnscaled, t);
                } else if (prevNarrowIdx === -1) {
                    // Before first narrow
                    const nextNarrowSegmentIndex = uniqueNarrowPositions[nextNarrowIdx];
                    // Ensure divisor is not zero (already handled by preventing narrow at index 0)
                    const t = i / nextNarrowSegmentIndex;
                    const smooth_t = 0.5 - 0.5 * Math.cos(t * Math.PI);
                    targetRadiusUnscaled = BABYLON.Scalar.Lerp(startRadiusUnscaled, narrowRadiusUnscaled, smooth_t);
                } else if (nextNarrowIdx === -1) {
                    // After last narrow
                    const prevNarrowSegmentIndex = uniqueNarrowPositions[prevNarrowIdx];
                    const denominator = (segments - 1 - prevNarrowSegmentIndex);
                    // Ensure divisor is not zero (already handled by preventing narrow at segments-1)
                    const t = denominator > 0 ? (i - prevNarrowSegmentIndex) / denominator : 1; // Avoid division by zero
                    const smooth_t = 0.5 - 0.5 * Math.cos(t * Math.PI);
                    targetRadiusUnscaled = BABYLON.Scalar.Lerp(narrowRadiusUnscaled, endRadiusUnscaled, smooth_t);
                } else {
                    // Between two narrows
                    const prevNarrowSegmentIndex = uniqueNarrowPositions[prevNarrowIdx];
                    const nextNarrowSegmentIndex = uniqueNarrowPositions[nextNarrowIdx];
                    const denominator = (nextNarrowSegmentIndex - prevNarrowSegmentIndex);
                    // Ensure divisor is not zero (shouldn't happen with unique sorted > 1 points)
                    const t = denominator > 0 ? (i - prevNarrowSegmentIndex) / denominator : 0.5; // Avoid division by zero
                    const sinFactor = Math.sin(t * Math.PI);
                    targetRadiusUnscaled = narrowRadiusUnscaled + (startRadiusUnscaled - narrowRadiusUnscaled) * sinFactor;
                    targetRadiusUnscaled = Math.min(targetRadiusUnscaled, startRadiusUnscaled * 1.05);
                }
            } // End interpolation logic

            // ** Ensure radius is never zero or negative **
            radii.push(Math.max(0.1, targetRadiusUnscaled * scaleFactor));


            // --- Update Direction ---
            let currentTurnFactor = turnFactor;
            let currentUpDownFactor = upDownFactor;
            if (isAtNarrowPeak) {
                currentTurnFactor *= 1.2;
                currentUpDownFactor *= 1.2;
            } else { /* ... adjust factors near narrows ... */
            }
            const randomRotX = (Math.random() - 0.5) * Math.PI * currentUpDownFactor;
            const randomRotY = (Math.random() - 0.5) * Math.PI * currentTurnFactor;
            const rotationMatrix = BABYLON.Matrix.RotationYawPitchRoll(randomRotY, randomRotX, 0);
            currentDir = BABYLON.Vector3.TransformNormal(currentDir, rotationMatrix).normalize();
            currentPos.addInPlace(currentDir.scale(segmentLength));
            // --- End Update Direction ---

        } // End for loop
    } catch (loopError) {
        console.error("Error occurred inside createTunnelPathWithNarrows loop:", loopError);
        return {path: [], radii: [], narrowPoints: []}; // Return empty on loop error
    }

    // Final validation
    if (path.length !== segments || radii.length !== segments) {
        console.error(`Path/Radii length (${path.length}/${radii.length}) doesn't match requested segments (${segments})!`);
        // Don't try to fix here, indicate failure
        return {path: [], radii: [], narrowPoints: []};
    }
    if (DEBUG_MODE) console.log(`Generated path/radii successfully: ${path.length} points.`);

    return {path, radii, narrowPoints: narrowPointsInfo};
}


/**
 * Analyzes the generated tunnel path and radii to create logical sections
 * used for gameplay calculations (e.g., finding player's section, checking narrows).
 * Populates the global 'tunnelSections' array.
 * @param {BABYLON.Vector3[]} path - Array of Vector3 points defining the tunnel center line.
 * @param {number[]} radii - Array of scaled radius values corresponding to each path point.
 * @param {number} radiusScaleFactor - The scale factor that was used to generate the 'radii' array.
 */
function analyzeAndCreateTunnelSections(path, radii, radiusScaleFactor) {
    tunnelSections.length = 0; // Clear previous sections

    // Validate input
    if (!path || path.length < 2 || !radii || radii.length < 2 || !radiusScaleFactor || radiusScaleFactor <= 0) {
        console.error("Cannot analyze tunnel: Invalid path, radii, or scaleFactor provided.", {
            pathLen: path?.length,
            radiiLen: radii?.length,
            scaleFactor: radiusScaleFactor
        });
        return;
    }
    if (path.length !== radii.length) {
        console.error(`Path length (${path.length}) and Radii length (${radii.length}) mismatch during analysis! Cannot proceed reliably.`);
        // Don't attempt fix here, error should be caught earlier or handled by caller
        return;
    }

    const totalSegments = path.length - 1;
    if (DEBUG_MODE) console.log(`Analyzing tunnel (Level ${gameLevel}): ${totalSegments} segments.`);

    // Pre-calculate the scaled start radius for threshold comparison
    const scaledStartRadius = Config.TUNNEL_DIAMETER_START * radiusScaleFactor;
    const narrowingThreshold = scaledStartRadius * 0.65; // Threshold is 65% of scaled start radius

    for (let i = 0; i < totalSegments; i++) {
        const startPoint = path[i];
        const endPoint = path[i + 1];
        const centerPoint = BABYLON.Vector3.Lerp(startPoint, endPoint, 0.5); // Midpoint
        const direction = endPoint.subtract(startPoint).normalize(); // Direction vector
        const length = BABYLON.Vector3.Distance(startPoint, endPoint); // Segment length

        // Calculate average scaled radius for this section
        const radiusStart = radii[i];
        const radiusEnd = radii[i + 1];
        const sectionRadius = (radiusStart + radiusEnd) / 2;

        // Determine if this section counts as 'narrowing' based on the threshold
        const isNarrowing = sectionRadius < narrowingThreshold;

        // Push the section data into the global array
        tunnelSections.push({
            startPoint: startPoint,
            endPoint: endPoint,
            centerPoint: centerPoint,
            direction: direction,
            index: i,
            radius: sectionRadius, // Store the average scaled radius
            length: length,
            narrowing: isNarrowing // Boolean flag
        });
    }

    if (DEBUG_MODE) console.log(`Finished analysis, created ${tunnelSections.length} logical tunnel sections.`);

    // Calculate FFR values for the tunnel sections
    if (typeof calculateTunnelFFR === 'function') {
        try {
            calculateTunnelFFR();
            if (DEBUG_MODE) console.log(`Calculated FFR values for ${tunnelSections.length} tunnel sections.`);
        } catch (error) {
            console.error("Error calculating FFR values:", error);
        }
    } else {
        if (DEBUG_MODE) console.warn("calculateTunnelFFR function is not defined - FFR values will not be calculated.");
    }
}


// Enhance overall scene lighting (e.g., ambient)
function enhanceSceneLighting(scene) {
    // Adjust existing Hemispheric light for better ambient fill
    scene.lights.forEach(light => {
        if (light instanceof BABYLON.HemisphericLight) {
            light.intensity = 0.5; // Slightly lower intensity if tunnel/player lights are strong
            light.diffuse = new BABYLON.Color3(0.7, 0.6, 0.8); // Keep colors relatively bright
            light.groundColor = new BABYLON.Color3(0.2, 0.1, 0.2);
            if (DEBUG_MODE) console.log("Adjusted existing HemisphericLight.");
        }
        // Optionally adjust other lights like Directional if they exist globally
        if (light instanceof BABYLON.DirectionalLight && light.name === "globalDirLight") {
            light.intensity = 0.3;
        }
    });

    // Add a subtle global directional light if one doesn't exist,
    // to ensure some directional definition even without player light
    let globalDirLight = scene.getLightByName("globalDirLight");
    if (!globalDirLight) {
        globalDirLight = new BABYLON.DirectionalLight("globalDirLight", new BABYLON.Vector3(0.2, -0.8, 0.5), scene); // Angled light
        globalDirLight.intensity = 0.25; // Keep it subtle
        globalDirLight.diffuse = new BABYLON.Color3(0.9, 0.85, 0.8); // Soft white-ish
        globalDirLight.specular = BABYLON.Color3.Black(); // No specular from this light
        if (DEBUG_MODE) console.log("Added subtle global DirectionalLight.");
    }
}


// Apply post-creation effects/fixes (like Fresnel) - Simplified for single Mesh
function fixTunnelLighting(scene) {
    enhanceSceneLighting(scene); // Ensure base lighting is set

    if (tunnelMesh && tunnelMesh instanceof BABYLON.Mesh && tunnelMesh.material) {
        if (DEBUG_MODE) console.log(`Attempting to ensure Fresnel effect on tunnel mesh: ${tunnelMesh.name}`);

        // Check if material is StandardMaterial or PBRMaterial which support Fresnel
        if (tunnelMesh.material instanceof BABYLON.StandardMaterial || tunnelMesh.material instanceof BABYLON.PBRMaterial) {
            // Check if Fresnel parameters already exist, apply if not or re-apply if needed
            if (!tunnelMesh.material.emissiveFresnelParameters) {
                try {
                    tunnelMesh.material.emissiveFresnelParameters = new BABYLON.FresnelParameters();
                    tunnelMesh.material.emissiveFresnelParameters.bias = 0.2;
                    tunnelMesh.material.emissiveFresnelParameters.power = 1;
                    tunnelMesh.material.emissiveFresnelParameters.leftColor = BABYLON.Color3.White();
                    tunnelMesh.material.emissiveFresnelParameters.rightColor = new BABYLON.Color3(0.5, 0.2, 0.2);
                    if (DEBUG_MODE) console.log("Applied Fresnel effect to tunnel mesh material.");
                } catch (e) {
                    console.error(`Error applying Fresnel to mesh ${tunnelMesh.name}:`, e); // Keep critical error
                }
            } else {
                // Optionally re-apply or just confirm existence in debug mode
                if (DEBUG_MODE) console.log("Fresnel parameters already exist on tunnel material.");
            }
        } else {
            if (DEBUG_MODE) console.warn(`Material on tunnel mesh ${tunnelMesh.name} (${tunnelMesh.material.getClassName()}) does not support Fresnel parameters.`);
        }

        // Apply FFR-based coloring to the tunnel mesh
        if (typeof window.applyFFRColoring === 'function') {
            try {
                window.applyFFRColoring();
                if (DEBUG_MODE) console.log("Applied FFR-based coloring to tunnel mesh with enhanced lighting.");
            } catch (ffrError) {
                console.error("Error applying FFR coloring to tunnel:", ffrError);
            }
        } else {
            if (DEBUG_MODE) console.warn("applyFFRColoring function is not defined - FFR coloring will not be applied.");
        }
    } else if (tunnelMesh && !(tunnelMesh instanceof BABYLON.Mesh)) {
        if (DEBUG_MODE) console.warn("fixTunnelLighting: tunnelMesh exists but is not a Mesh. Type:", tunnelMesh.getClassName());
    } else if (tunnelMesh && !tunnelMesh.material) {
        if (DEBUG_MODE) console.warn("fixTunnelLighting: tunnelMesh exists but has no material.");
    } else {
        if (DEBUG_MODE) console.warn("fixTunnelLighting called, but tunnelMesh does not exist or is invalid.");
    }
}
/**
 * Returns the current tunnel mesh
 * @returns {BABYLON.Mesh|null} The current tunnel mesh or null if not created
 */
function getTunnelMesh() {
    return tunnelMesh;
}

/**
 * Returns the current tunnel sections
 * @returns {Array} Array of tunnel sections
 */
function getTunnelSections() {
    return tunnelSections;
}
// Explicitly expose functions to global scope for module scripts
window.createTunnel = createTunnel;
window.getTunnelMesh = getTunnelMesh;
window.getTunnelSections = getTunnelSections;
window.fixTunnelLighting = fixTunnelLighting;

// --- Tunnel Improvements Integration ---

/**
 * Sprawdza, czy gracz styka się ze ścianą tunelu
 * @param {BABYLON.Vector3} playerPosition
 * @param {Object} section
 * @returns {boolean}
 */
function isPlayerTouchingTunnelWall(playerPosition, section) {
    if (!playerPosition || !section) return false;
    // Oblicz wektor od środka tunelu do gracza
    const playerOffset = playerPosition.subtract(section.centerPoint);
    // Usuń składową wzdłuż osi tunelu
    const axialComponent = BABYLON.Vector3.Dot(playerOffset, section.direction) * section.direction;
    const radialOffset = playerOffset.subtract(axialComponent);
    const distanceFromAxis = radialOffset.length();
    // Uznajemy, że gracz styka się ze ścianą, jeśli jest bardzo blisko promienia tunelu (np. 95%)
    return distanceFromAxis >= section.radius * 0.95;
}

/**
 * Ogranicza pozycję kamery do wnętrza tunelu z płynnym wpływem w zależności od odległości gracza od ściany
 * @param {BABYLON.Camera} camera - Kamera do ograniczenia
 * @param {BABYLON.Vector3} playerPosition - Pozycja gracza
 * @param {Object} options - Opcjonalne parametry
 */
function constrainCameraToTunnel(camera, playerPosition, options = {}) {
    if (!camera || !playerPosition || !tunnelSections || tunnelSections.length === 0) return;

    // Znajdź aktualną sekcję tunelu
    const section = options.section || findSectionAtPosition(camera.position.z);
    if (!section) return;

    // Oblicz wektor od środka tunelu do kamery
    const cameraOffset = camera.position.subtract(section.centerPoint);

    // Usuń składową wzdłuż osi tunelu
    const axialComponent = BABYLON.Vector3.Dot(cameraOffset, section.direction) * section.direction;
    const radialOffset = cameraOffset.subtract(axialComponent);

    // Sprawdź odległość od osi tunelu
    const distanceFromAxis = radialOffset.length();

    // Maksymalna dozwolona odległość kamery od osi (70% promienia tunelu)
    const maxCameraDistance = section.radius * 0.7;

    // --- Płynny wpływ ograniczenia kamery w zależności od odległości gracza od ściany ---
    // Oblicz odległość gracza od ściany (0 = środek, 1 = ściana)
    const playerOffset = playerPosition.subtract(section.centerPoint);
    const playerAxialComponent = BABYLON.Vector3.Dot(playerOffset, section.direction) * section.direction;
    const playerRadialOffset = playerOffset.subtract(playerAxialComponent);
    const playerDistanceFromAxis = playerRadialOffset.length();
    const wallThreshold = section.radius * 0.95;
    let influence = 0;
    if (playerDistanceFromAxis >= wallThreshold) {
        influence = 1;
    } else {
        // Im bliżej ściany, tym większy wpływ (od 0.0 do 1.0)
        influence = Math.max(0, (playerDistanceFromAxis - (section.radius * 0.7)) / (wallThreshold - (section.radius * 0.7)));
        influence = Math.min(1, influence);
    }

    if (distanceFromAxis > maxCameraDistance && influence > 0) {
        const correctionFactor = maxCameraDistance / distanceFromAxis;
        const correctedRadialOffset = radialOffset.scale(correctionFactor);
        const newCameraPosition = section.centerPoint.add(axialComponent).add(correctedRadialOffset);
        const lerpFactor = options.lerpFactor !== undefined ? options.lerpFactor : 0.3;
        // Płynnie mieszaj pozycję kamery w zależności od influence
        camera.position = BABYLON.Vector3.Lerp(camera.position, newCameraPosition, lerpFactor * influence);
    }

    // Upewnij się, że kamera nie jest poniżej tunelu (bardziej restrykcyjne ograniczenie)
    // Minimalna dozwolona pozycja Y: środek tunelu minus promień (podłoga tunelu)
    const minY = section.centerPoint.y - section.radius;
    if (camera.position.y < minY) {
        // Natychmiast przesuń kamerę do minimalnej pozycji Y (brak efektu "wpadania pod podłogę")
        camera.position.y = minY;
    }

    // Dodatkowe ograniczenie: nie pozwól kamerze być zbyt daleko za graczem
    const maxDistanceBehindPlayer = 3.0;
    const playerToCameraZ = camera.position.z - playerPosition.z;

    if (playerToCameraZ > maxDistanceBehindPlayer) {
        camera.position.z = playerPosition.z + maxDistanceBehindPlayer;
    }

    // Nie pozwól kamerze być przed graczem
    if (playerToCameraZ < 0) {
        camera.position.z = playerPosition.z + 0.1;
    }
}

/**
 * Aktualizuje pozycję kamery z ograniczeniem do tunelu
 * Zastępuje obecną logikę kamery w gameLoop
 */
function updateCameraWithConstraints() {
    // Nie aktualizuj kamery, jeśli trwa przejście między trybami
    if (window.cameraTransitionInProgress) return;

    if (!camera || !bunnyCollider || !cameraReachedPlayer) return;

    const currentSection = findSectionAtPosition(bunnyCollider.position.z);
    if (!currentSection) return;

    // Sprawdź, czy zbliżamy się do granicy sekcji
    let nextSection = null;
    let blendFactor = 0;

    // Znajdź granicę między sekcjami
    if (currentSectionIndex < tunnelSections.length - 1) {
        nextSection = tunnelSections[currentSectionIndex + 1];

        // Oblicz odległość do granicy sekcji
        const sectionEndZ = Math.max(currentSection.startPoint.z, currentSection.endPoint.z);
        const distanceToSectionEnd = Math.abs(bunnyCollider.position.z - sectionEndZ);

        // Jeśli jesteśmy blisko granicy (w odległości 2 jednostek), rozpocznij interpolację
        const blendDistance = 2.0;
        if (distanceToSectionEnd < blendDistance) {
            blendFactor = 1.0 - (distanceToSectionEnd / blendDistance);
        }
    }

    if (cameraMode === 'TPP') {
        // Kamera trzecioosobowa
        let offsetY = 1.2;  // Zmniejszone z 1.5
        let offsetZ = 1.8;  // Zmniejszone z 2.0

        // Dynamiczne dostosowanie offsetu do rozmiaru tunelu z płynnym przejściem między sekcjami
        let tunnelConstraintFactor;

        if (nextSection && blendFactor > 0) {
            // Interpolacja między bieżącą a następną sekcją
            const currentConstraint = Math.min(1.0, currentSection.radius / 2.0);
            const nextConstraint = Math.min(1.0, nextSection.radius / 2.0);
            tunnelConstraintFactor = currentConstraint * (1 - blendFactor) + nextConstraint * blendFactor;
        } else {
            tunnelConstraintFactor = Math.min(1.0, currentSection.radius / 2.0);
        }

        offsetY *= tunnelConstraintFactor;
        offsetZ *= tunnelConstraintFactor;

        // Oblicz pozycję kamery względem gracza
        const playerForward = bunnyCollider.getDirection(BABYLON.Axis.Z);
        const playerUp = BABYLON.Vector3.Up();

        // Pozycja kamery za i nad graczem
        const cameraOffset = playerForward.scale(offsetZ)
            .add(playerUp.scale(offsetY));

        const targetCameraPosition = bunnyCollider.position.add(cameraOffset);

        // Płynne przejście do docelowej pozycji
        camera.position = BABYLON.Vector3.Lerp(
            camera.position,
            targetCameraPosition,
            0.15  // Zmniejszona szybkość śledzenia dla płynniejszego ruchu kamery
        );

        // Ustaw cel kamery na gracza
        camera.setTarget(bunnyCollider.position);

        // Zastosuj ograniczenia tunelu z uwzględnieniem płynnego przejścia
        if (nextSection && blendFactor > 0) {
            // Interpolacja między ograniczeniami bieżącej i następnej sekcji
            const currentCameraPos = camera.position.clone();

            // Zastosuj ograniczenia dla bieżącej sekcji
            constrainCameraToTunnel(camera, bunnyCollider.position, {
                section: currentSection,
                lerpFactor: 0.3 * (1 - blendFactor) // Zmniejsz wpływ bieżącej sekcji gdy zbliżamy się do granicy
            });
            const constrainedCurrentPos = camera.position.clone();

            // Przywróć oryginalną pozycję
            camera.position = currentCameraPos;

            // Zastosuj ograniczenia dla następnej sekcji
            constrainCameraToTunnel(camera, bunnyCollider.position, {
                section: nextSection,
                lerpFactor: 0.3 * blendFactor // Zwiększ wpływ następnej sekcji gdy zbliżamy się do granicy
            });
            const constrainedNextPos = camera.position.clone();

            // Interpolacja między ograniczonymi pozycjami
            camera.position = BABYLON.Vector3.Lerp(
                constrainedCurrentPos,
                constrainedNextPos,
                blendFactor
            );
        } else {
            // Standardowe ograniczenie dla bieżącej sekcji
            constrainCameraToTunnel(camera, bunnyCollider.position, {
                section: currentSection,
                lerpFactor: 0.3
            });
        }

    } else if (cameraMode === 'FPP') {
        // Kamera pierwszoosobowa
        const offset = new BABYLON.Vector3(0, -0.3, 0); // Zmniejszone przesunięcie w dół

        // Płynne przejście do docelowej pozycji
        const targetPosition = bunnyCollider.position.add(offset);
        camera.position = BABYLON.Vector3.Lerp(
            camera.position,
            targetPosition,
            0.3 // Płynne przejście
        );

        // Kierunek patrzenia wzdłuż tunelu z płynnym przejściem między sekcjami
        let forwardDirection;

        if (nextSection && blendFactor > 0) {
            // Interpolacja między kierunkami bieżącej i następnej sekcji
            const currentDirection = currentSection.direction.scale(-1);
            const nextDirection = nextSection.direction.scale(-1);

            // Normalizuj wektory przed interpolacją
            currentDirection.normalize();
            nextDirection.normalize();

            // Interpolacja liniowa między wektorami kierunków
            forwardDirection = BABYLON.Vector3.Lerp(
                currentDirection,
                nextDirection,
                blendFactor
            );

            // Normalizuj wynikowy wektor
            forwardDirection.normalize();
        } else {
            forwardDirection = currentSection.direction.scale(-1);
        }

        camera.setTarget(camera.position.add(forwardDirection));

        // Minimalne ograniczenia dla FPP z uwzględnieniem płynnego przejścia
        if (nextSection && blendFactor > 0) {
            // Podobna logika jak dla TPP, ale z mniejszym wpływem na pozycję kamery
            const currentCameraPos = camera.position.clone();

            // Zastosuj ograniczenia dla bieżącej sekcji
            constrainCameraToTunnel(camera, bunnyCollider.position, {
                section: currentSection,
                lerpFactor: 0.2 * (1 - blendFactor) // Mniejszy wpływ dla FPP
            });
            const constrainedCurrentPos = camera.position.clone();

            // Przywróć oryginalną pozycję
            camera.position = currentCameraPos;

            // Zastosuj ograniczenia dla następnej sekcji
            constrainCameraToTunnel(camera, bunnyCollider.position, {
                section: nextSection,
                lerpFactor: 0.2 * blendFactor // Mniejszy wpływ dla FPP
            });
            const constrainedNextPos = camera.position.clone();

            // Interpolacja między ograniczonymi pozycjami
            camera.position = BABYLON.Vector3.Lerp(
                constrainedCurrentPos,
                constrainedNextPos,
                blendFactor
            );
        } else {
            // Standardowe ograniczenie dla bieżącej sekcji
            constrainCameraToTunnel(camera, bunnyCollider.position, {
                section: currentSection,
                lerpFactor: 0.2 // Mniejszy wpływ dla FPP
            });
        }
    }
}

/**
 * Sprawdza czy punkt znajduje się wewnątrz tunelu
 * @param {BABYLON.Vector3} position - Pozycja do sprawdzenia
 * @returns {boolean} True jeśli punkt jest w tunelu
 */
function isPositionInTunnel(position) {
    if (!position || !tunnelSections || tunnelSections.length === 0) return false;

    const section = findSectionAtPosition(position.z);
    if (!section) return false;

    // Sprawdź, czy zbliżamy się do granicy sekcji
    let nextSection = null;
    let blendFactor = 0;

    // Znajdź granicę między sekcjami
    if (currentSectionIndex < tunnelSections.length - 1) {
        nextSection = tunnelSections[currentSectionIndex + 1];

        // Oblicz odległość do granicy sekcji
        const sectionEndZ = Math.max(section.startPoint.z, section.endPoint.z);
        const distanceToSectionEnd = Math.abs(position.z - sectionEndZ);

        // Jeśli jesteśmy blisko granicy (w odległości 2 jednostek), rozpocznij interpolację
        const blendDistance = 2.0;
        if (distanceToSectionEnd < blendDistance) {
            blendFactor = 1.0 - (distanceToSectionEnd / blendDistance);
        }
    }

    // Funkcja do sprawdzania czy pozycja jest w tunelu dla danej sekcji
    function isInSectionTunnel(pos, sec) {
        const toPos = pos.subtract(sec.centerPoint);
        const axialComp = BABYLON.Vector3.Dot(toPos, sec.direction);
        const projPoint = sec.centerPoint.add(sec.direction.scale(axialComp));
        const distFromAxis = BABYLON.Vector3.Distance(pos, projPoint);

        // Sprawdź czy jest w promieniu tunelu (z małym marginesem)
        return distFromAxis <= sec.radius * 0.95;
    }

    // Jeśli jesteśmy blisko granicy sekcji, sprawdź obie sekcje
    if (nextSection && blendFactor > 0) {
        // Sprawdź czy pozycja jest w bieżącej sekcji
        const inCurrentSection = isInSectionTunnel(position, section);

        // Sprawdź czy pozycja jest w następnej sekcji
        const inNextSection = isInSectionTunnel(position, nextSection);

        // Jeśli pozycja jest w którejkolwiek sekcji, uznaj ją za wewnątrz tunelu
        // Im bliżej granicy, tym większe znaczenie ma następna sekcja
        return (inCurrentSection && blendFactor < 0.9) || (inNextSection && blendFactor > 0.1);
    } else {
        // Standardowe sprawdzenie dla bieżącej sekcji
        return isInSectionTunnel(position, section);
    }
}

/**
 * Przyciąga obiekt do wnętrza tunelu jeśli jest poza nim
 * @param {BABYLON.Vector3} position - Pozycja obiektu
 * @param {number} margin - Margines od ściany tunelu (0-1)
 * @returns {BABYLON.Vector3} Skorygowana pozycja
 */
function constrainPositionToTunnel(position, margin = 0.9) {
    if (!position || !tunnelSections || tunnelSections.length === 0) return position;

    const section = findSectionAtPosition(position.z);
    if (!section) return position;

    // Sprawdź, czy zbliżamy się do granicy sekcji
    let nextSection = null;
    let blendFactor = 0;

    // Znajdź granicę między sekcjami
    if (currentSectionIndex < tunnelSections.length - 1) {
        nextSection = tunnelSections[currentSectionIndex + 1];

        // Oblicz odległość do granicy sekcji
        const sectionEndZ = Math.max(section.startPoint.z, section.endPoint.z);
        const distanceToSectionEnd = Math.abs(position.z - sectionEndZ);

        // Jeśli jesteśmy blisko granicy (w odległości 2 jednostek), rozpocznij interpolację
        const blendDistance = 2.0;
        if (distanceToSectionEnd < blendDistance) {
            blendFactor = 1.0 - (distanceToSectionEnd / blendDistance);
        }
    }

    // Funkcja do obliczania skorygowanej pozycji dla danej sekcji
    function getConstrainedPositionForSection(pos, sec, m) {
        const toPos = pos.subtract(sec.centerPoint);
        const axialComp = BABYLON.Vector3.Dot(toPos, sec.direction);
        const projPoint = sec.centerPoint.add(sec.direction.scale(axialComp));
        const radVec = pos.subtract(projPoint);
        const distFromAxis = radVec.length();

        // Maksymalna dozwolona odległość
        const maxDist = sec.radius * m;

        // Jeśli obiekt jest za daleko, przyciągnij go
        if (distFromAxis > maxDist) {
            const normRadial = radVec.normalize();
            return projPoint.add(normRadial.scale(maxDist));
        }

        return pos.clone();
    }

    // Jeśli jesteśmy blisko granicy sekcji, interpoluj między ograniczeniami
    if (nextSection && blendFactor > 0) {
        // Oblicz skorygowaną pozycję dla bieżącej sekcji
        const currentConstrainedPos = getConstrainedPositionForSection(position, section, margin);

        // Oblicz skorygowaną pozycję dla następnej sekcji
        const nextConstrainedPos = getConstrainedPositionForSection(position, nextSection, margin);

        // Interpoluj między pozycjami
        return BABYLON.Vector3.Lerp(currentConstrainedPos, nextConstrainedPos, blendFactor);
    } else {
        // Standardowe ograniczenie dla bieżącej sekcji
        return getConstrainedPositionForSection(position, section, margin);
    }
}

/**
 * Ulepszona funkcja kolorowania FFR z płynnymi gradientami
 */
function applyGradientFFRColoring() {
    const tunnelMesh = getTunnelMesh();
    if (!tunnelMesh) {
        console.warn("No tunnel mesh available for FFR coloring");
        return;
    }

    const tunnelSections = getTunnelSections();
    if (!tunnelSections || tunnelSections.length === 0) {
        console.warn("No tunnel sections available for FFR coloring");
        return;
    }

    // Oblicz kolory FFR
    const ffrColors = calculateTunnelFFR();
    if (!ffrColors || ffrColors.length === 0) {
        console.warn("No FFR colors calculated");
        return;
    }

    try {
        // Utwórz shader material dla gradientowego kolorowania
        const shaderMaterial = new BABYLON.ShaderMaterial("tunnelFFRShader", scene, {
            vertex: "tunnelFFR",
            fragment: "tunnelFFR",
        }, {
            attributes: ["position", "normal", "uv"],
            uniforms: ["world", "worldView", "worldViewProjection", "view", "projection",
                       "ffrColors", "sectionCount", "tunnelLength", "cameraPosition"],
            samplers: ["diffuseTexture", "normalTexture"]
        });

        // Przygotuj tablicę kolorów dla shadera
        const colorArray = [];
        for (let i = 0; i < ffrColors.length; i++) {
            colorArray.push(ffrColors[i].r, ffrColors[i].g, ffrColors[i].b, 1.0);
        }

        // Ustaw uniformy
        shaderMaterial.setFloats("ffrColors", colorArray);
        shaderMaterial.setFloat("sectionCount", tunnelSections.length);
        shaderMaterial.setFloat("tunnelLength",
            BABYLON.Vector3.Distance(
                tunnelSections[0].startPoint,
                tunnelSections[tunnelSections.length - 1].endPoint
            )
        );
        shaderMaterial.setVector3("cameraPosition", scene.activeCamera.position);

        // Ustaw tekstury
        const diffuseTexture = new BABYLON.Texture("./textures/others_0003_color_1k.jpg", scene);
        shaderMaterial.setTexture("diffuseTexture", diffuseTexture);

        try {
            // Try to load OpenGL format normal map first
            const normalTexture = new BABYLON.Texture("./textures/others_0003_normal_opengl_1k.png", scene);
            // Set texture properties for normal mapping
            normalTexture.uScale = 3.0;
            normalTexture.vScale = 3.0;
            normalTexture.hasAlpha = false;
            shaderMaterial.setTexture("normalTexture", normalTexture);
            console.log("Normal texture (OpenGL format) loaded successfully");
        } catch (e) {
            console.warn("OpenGL normal texture not available, trying DirectX format");
            try {
                // Fallback to DirectX format if OpenGL format fails
                const normalTexture = new BABYLON.Texture("./textures/others_0003_normal_directx_1k.png", scene);
                // Set texture properties for normal mapping
                normalTexture.uScale = 3.0;
                normalTexture.vScale = 3.0;
                normalTexture.hasAlpha = false;
                // For DirectX format normal maps, we need to invert the green channel
                normalTexture.onLoadObservable.add(() => {
                    console.log("DirectX normal texture loaded, inverting green channel");
                    // This is handled in the shader
                });
                shaderMaterial.setTexture("normalTexture", normalTexture);
                console.log("Normal texture (DirectX format) loaded successfully");
            } catch (e2) {
                console.error("Failed to load any normal texture:", e2);
            }
        }

        // Właściwości materiału
        shaderMaterial.backFaceCulling = false;

        // Zastosuj materiał do tunelu
        tunnelMesh.material = shaderMaterial;

        // Aktualizuj pozycję kamery w shaderze co klatkę
        scene.registerBeforeRender(() => {
            if (scene.activeCamera) {
                shaderMaterial.setVector3("cameraPosition", scene.activeCamera.position);
            }
        });

        console.log("Applied gradient FFR coloring with shader");
    } catch (error) {
        console.error("Error applying gradient FFR coloring:", error);

        // Fallback do standardowego kolorowania
        applyStandardFFRColoring(tunnelMesh, tunnelSections, ffrColors);
    }
}

/**
 * Tworzy vertex i fragment shadery dla gradientowego FFR
 */
function createFFRShaders() {
    // Vertex shader
    const vertexShader = `
        precision highp float;

        // Attributes
        attribute vec3 position;
        attribute vec3 normal;
        attribute vec2 uv;

        // Uniforms
        uniform mat4 world;
        uniform mat4 worldView;
        uniform mat4 worldViewProjection;

        // Varying
        varying vec3 vPositionW;
        varying vec3 vNormalW;
        varying vec2 vUV;
        varying float vDistanceAlongTunnel;

        void main(void) {
            vec4 worldPos = world * vec4(position, 1.0);
            vPositionW = worldPos.xyz;
            vNormalW = normalize((world * vec4(normal, 0.0)).xyz);
            vUV = uv;

            // Oblicz odległość wzdłuż tunelu (zakładamy że tunel biegnie głównie wzdłuż osi Z)
            vDistanceAlongTunnel = worldPos.z;

            gl_Position = worldViewProjection * vec4(position, 1.0);
        }
    `;

    // Fragment shader
    const fragmentShader = `
        precision highp float;

        // Uniforms
        uniform vec3 cameraPosition;
        uniform float ffrColors[400]; // Max 100 sections * 4 components (RGBA)
        uniform float sectionCount;
        uniform float tunnelLength;

        // Samplers
        uniform sampler2D diffuseTexture;
        uniform sampler2D normalTexture;

        // Varying
        varying vec3 vPositionW;
        varying vec3 vNormalW;
        varying vec2 vUV;
        varying float vDistanceAlongTunnel;

        vec3 getFFRColorAtPosition(float position) {
            // Normalizuj pozycję do zakresu 0-1
            float normalizedPos = clamp(position / tunnelLength, 0.0, 1.0);

            // Znajdź indeks sekcji
            float sectionIndex = normalizedPos * (sectionCount - 1.0);
            int index1 = int(floor(sectionIndex));
            int index2 = int(ceil(sectionIndex));
            float t = fract(sectionIndex);

            // Pobierz kolory dla interpolacji
            vec3 color1 = vec3(
                ffrColors[index1 * 4],
                ffrColors[index1 * 4 + 1],
                ffrColors[index1 * 4 + 2]
            );
            vec3 color2 = vec3(
                ffrColors[index2 * 4],
                ffrColors[index2 * 4 + 1],
                ffrColors[index2 * 4 + 2]
            );

            // Interpolacja z funkcją wygładzającą
            t = smoothstep(0.0, 1.0, t);
            return mix(color1, color2, t);
        }

        void main(void) {
            // Pobierz kolor FFR dla aktualnej pozycji
            vec3 ffrColor = getFFRColorAtPosition(vDistanceAlongTunnel);

            // Pobierz kolor z tekstury
            vec4 textureColor = texture2D(diffuseTexture, vUV);

            // Pobierz normalną z tekstury normalnej i przekształć ją z zakresu [0,1] do [-1,1]
            vec3 normalFromMap = texture2D(normalTexture, vUV).rgb * 2.0 - 1.0;

            // Obsługa formatu DirectX (odwrócony kanał zielony)
            // Sprawdzamy czy normalFromMap.y jest głównie ujemny, co sugeruje format DirectX
            if (normalFromMap.y < 0.0 && length(normalFromMap) > 0.5) {
                normalFromMap.y = -normalFromMap.y; // Odwróć kanał zielony dla formatu DirectX
            }

            // Zwiększ siłę efektu normal mappingu
            float bumpStrength = 1.5; // Wartość > 1.0 zwiększa efekt, < 1.0 zmniejsza
            normalFromMap.xy *= bumpStrength; // Skaluj tylko komponenty x i y, zachowując z
            normalFromMap = normalize(normalFromMap); // Normalizuj po skalowaniu

            // Utwórz przestrzeń TBN (Tangent, Bitangent, Normal)
            vec3 normal = normalize(vNormalW);
            vec3 tangent = normalize(cross(normal, vec3(0.0, 1.0, 0.0)));
            if (length(tangent) < 0.01) {
                tangent = normalize(cross(normal, vec3(1.0, 0.0, 0.0)));
            }
            vec3 bitangent = normalize(cross(normal, tangent));

            // Przekształć normalną z przestrzeni tekstury do przestrzeni świata
            mat3 TBN = mat3(tangent, bitangent, normal);
            vec3 worldNormal = normalize(TBN * normalFromMap);

            // Podstawowe oświetlenie kierunkowe
            vec3 lightDirection = normalize(vec3(0.5, 1.0, 0.5));
            float diffuseFactor = max(dot(worldNormal, lightDirection), 0.0);
            vec3 diffuseLight = vec3(1.0, 1.0, 1.0) * diffuseFactor;

            // Oświetlenie otoczenia
            vec3 ambientLight = vec3(0.3, 0.3, 0.3);

            // Mieszaj kolor tekstury z kolorem FFR
            vec3 baseColor = mix(textureColor.rgb, ffrColor, 0.6);

            // Zastosuj oświetlenie do koloru bazowego
            baseColor = baseColor * (ambientLight + diffuseLight);

            // Dodaj emisję dla lepszej widoczności FFR
            vec3 emission = ffrColor * 0.4;

            // Oblicz oświetlenie Fresnela używając normalnej z mapy
            vec3 viewDirection = normalize(cameraPosition - vPositionW);
            float fresnel = pow(1.0 - max(dot(viewDirection, worldNormal), 0.0), 2.0);

            // Dodaj efekt Fresnel dla krawędzi
            vec3 fresnelColor = ffrColor * fresnel * 0.5;

            // Finalna kompozycja
            vec3 finalColor = baseColor + emission + fresnelColor;

            gl_FragColor = vec4(finalColor, 1.0);
        }
    `;

    // Zapisz shadery do użycia
    BABYLON.Effect.ShadersStore["tunnelFFRVertexShader"] = vertexShader;
    BABYLON.Effect.ShadersStore["tunnelFFRFragmentShader"] = fragmentShader;
}

/**
 * Inicjalizuje wszystkie poprawki tunelu
 */
function initializeTunnelImprovements() {
    // Utwórz shadery FFR
    createFFRShaders();

    // Zastąp standardową funkcję aktualizacji kamery
    if (typeof window.updateCameraPosition === 'function') {
        window.originalUpdateCameraPosition = window.updateCameraPosition;
        window.updateCameraPosition = updateCameraWithConstraints;
    }

    // Zastąp funkcję kolorowania FFR
    if (typeof window.applyFFRColoring === 'function') {
        window.originalApplyFFRColoring = window.applyFFRColoring;
        window.applyFFRColoring = applyGradientFFRColoring;
    }

    console.log("Tunnel improvements initialized");
}

// Eksportuj funkcje
window.isPlayerTouchingTunnelWall = isPlayerTouchingTunnelWall;
window.constrainCameraToTunnel = constrainCameraToTunnel;
window.updateCameraWithConstraints = updateCameraWithConstraints;
window.isPositionInTunnel = isPositionInTunnel;
window.constrainPositionToTunnel = constrainPositionToTunnel;
window.applyGradientFFRColoring = applyGradientFFRColoring;
window.createFFRShaders = createFFRShaders;
window.initializeTunnelImprovements = initializeTunnelImprovements;

// --- END OF FILE world.js ---
