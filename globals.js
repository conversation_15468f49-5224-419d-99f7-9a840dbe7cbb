// --- START OF FILE globals.js ---

// --- GLOBAL CONFIGURATION FLAG ---
// Define DEBUG_MODE here, FIRST, so all other scripts can see it.
const DEBUG_MODE = true; // Set to true for development/debugging, false for release
// Expose DEBUG_MODE to window object so it's globally accessible
window.DEBUG_MODE = DEBUG_MODE;
// *** ADDED: Game Level ***
let gameLevel = 1;
let playerId = null;
let playerName = '';
let playerColor = { name: 'blue', color: null };
let otherPlayers = {};
let wpsclient = null;
let gameGroupId = null;
// Globalny system krwinek
let bloodCellSystem = null;

// --- Coronary Artery Segments Data ---
const coronarySegments = [
    {
        level: 1,
        segment: "Prox RCA",
        name: "Proximal segment of the right coronary artery",
        diameter: 3.78,
        description: "From its ostium in the aorta to halfway along the acute margin; gives off the conus and nodal branches"
    },
    {
        level: 2,
        segment: "Mid RCA",
        name: "Mid segment of the right coronary artery",
        diameter: 3.19,
        description: "From halfway along the acute margin to the acute margin itself"
    },
    {
        level: 3,
        segment: "Dist RCA",
        name: "Distal segment of the right coronary artery",
        diameter: 1.89,
        description: "From the acute margin to the origin of the posterior descending artery"
    },
    {
        level: 4,
        segment: "PDA (R)",
        name: "Right posterior descending artery",
        diameter: 2.0,
        description: "Runs in the posterior interventricular sulcus; may be absent in left-dominant hearts"
    },
    {
        level: 5,
        segment: "LMCA",
        name: "Left main coronary artery",
        diameter: 4.14,
        description: "From its ostium to the bifurcation into LAD and LCx"
    },
    {
        level: 6,
        segment: "Prox LAD",
        name: "Proximal left anterior descending artery",
        diameter: 3.12,
        description: "From the LMCA bifurcation to the first septal perforator"
    },
    {
        level: 7,
        segment: "Mid LAD",
        name: "Mid left anterior descending artery",
        diameter: 2.40,
        description: "From the first septal perforator to halfway toward the apex"
    },
    {
        level: 8,
        segment: "Dist LAD",
        name: "Distal left anterior descending artery",
        diameter: 1.29,
        description: "From halfway toward the apex all the way to the apex"
    },
    {
        level: 9,
        segment: "D1",
        name: "First diagonal branch",
        diameter: 2.0,
        description: "Runs obliquely over the anterior surface of the left ventricle"
    },
    {
        level: 10,
        segment: "D2",
        name: "Second diagonal branch",
        diameter: 1.5,
        description: "Second oblique branch over the anterior surface of the left ventricle"
    },
    {
        level: 11,
        segment: "Prox LCX",
        name: "Proximal left circumflex artery",
        diameter: 2.86,
        description: "From the LMCA bifurcation to the first obtuse marginal branch"
    },
    {
        level: 12,
        segment: "OM1",
        name: "First obtuse marginal branch",
        diameter: 2.5,
        description: "Largest obtuse marginal branch off the LCx"
    },
    {
        level: 13,
        segment: "Dist LCX",
        name: "Distal left circumflex artery",
        diameter: 1.31,
        description: "From the first obtuse marginal branch to the end of the LCx"
    },
    {
        level: 14,
        segment: "OM2",
        name: "Second obtuse marginal branch",
        diameter: 1.9,
        description: "Second obtuse marginal branch off the LCx"
    },
    {
        level: 15,
        segment: "PDA (L)",
        name: "Left posterior descending artery",
        diameter: 2.0,
        description: "Analogous to the right PDA, present in left-dominant systems"
    },
    {
        level: 16,
        segment: "RPL",
        name: "Right posterolateral branch",
        diameter: 2.1,
        description: "Runs in the right posterior lateral wall"
    },
    {
        level: 17,
        segment: "RI",
        name: "Intermediate branch (ramus intermedius)",
        diameter: 2.3,
        description: "Third branch from the LMCA, between LAD and LCx"
    },
    {
        level: 18,
        segment: "LPL",
        name: "Left posterolateral branch",
        diameter: 1.8,
        description: "Runs in the left posterior lateral wall"
    }
];

// --- AHA Atherosclerotic Lesion Types Data ---
// Moved from objects.js to be globally accessible for UI display
const atheroscleroticTypes = [
    {
        type: 1,
        polishName: "Zmiana początkowa",
        englishName: "Initial lesion",
        morphology: "Izolowane makrofagi i komórki piankowate w warstwie wewnętrznej (intima).",
        color: "#FFD700", // Gold - Should match color in objects.js
        emissive: new BABYLON.Color3(0.3, 0.3, 0.05),
        isDestructible: true,
        size: {min: 0.4, max: 0.7}, // Size data is useful for display context
        texturePath: "textures/type1.jpg" // Added texture path
    },
    {
        type: 2,
        polishName: "Smuga tłuszczowa",
        englishName: "Fatty streak",
        morphology: "Pofałdowanie intimy z licznymi komórkami piankowatymi (makrofagi i mięśniowe komórki gładkie) zawierającymi lipidy.",
        color: "#FFA500", // Orange - Should match color in objects.js
        emissive: new BABYLON.Color3(0.3, 0.2, 0.05),
        isDestructible: true,
        size: {min: 0.5, max: 0.8},
        texturePath: "textures/type2.jpg" // Added texture path
    },
    {
        type: 3,
        polishName: "Zmiana pośrednia",
        englishName: "Intermediate lesion",
        morphology: "Drobne, rozproszone zewnątrzkomórkowe depozyty lipidowe w intima, zakłócające przebieg komórek.",
        color: "#FF6347", // Tomato - Should match color in objects.js
        emissive: new BABYLON.Color3(0.3, 0.1, 0.05),
        isDestructible: true,
        size: {min: 0.6, max: 0.9},
        texturePath: "textures/type3.jpg" // Added texture path
    },
    {
        type: 4,
        polishName: "Ateroma",
        englishName: "Atheroma",
        morphology: "Wyraźne jądro zewnątrzkomórkowego, zgromadzonego lipidów tworzące istotny core lipidowy (\"necrotic core\").",
        color: "#e74c3c", // Red - Should match color in objects.js
        emissive: new BABYLON.Color3(0.3, 0.05, 0.05),
        isDestructible: false,
        size: {min: 0.7, max: 1.0},
        texturePath: "textures/type4.jpg" // Added texture path
    },
    {
        type: 5,
        polishName: "Fibroateroma",
        englishName: "Fibroatheroma",
        morphology: "Ateroma otoczone masywną torebką włóknistą; podtypy: Va (zwłókniałe core z lipidami), Vb (znaczna zwapnienia), Vc (głównie tkanka włóknista).",
        color: "#9b59b6", // Purple - Should match color in objects.js
        emissive: new BABYLON.Color3(0.15, 0.05, 0.2),
        isDestructible: false,
        size: {min: 0.8, max: 1.1},
        texturePath: "textures/type5.jpg" // Added texture path
    },
    {
        type: 6,
        polishName: "Zmiana skomplikowana",
        englishName: "Complicated lesion",
        morphology: "Defekt powierzchni (pęknięcie lub erozja), wewnątrzplaque'owy krwiak, krwotok, zakrzepica.",
        color: "#800000", // Maroon - Should match color in objects.js
        emissive: new BABYLON.Color3(0.2, 0.02, 0.02),
        isDestructible: false,
        size: {min: 0.9, max: 1.2},
        texturePath: "textures/type6.jpg" // Added texture path
    }
];

// --- SYSTEM CZĄSTECZEK DLA KRWINEK CZERWONYCH (PARTICLE SYSTEM) ---
function createBloodCellParticleSystem(scene, emitterPosition, tunnelRadius) {
    // Usuń poprzedni system cząsteczek jeśli istnieje
    if (window.bloodCellSystem) {
        window.bloodCellSystem.dispose();
        window.bloodCellSystem = null;
    }

    // ZOPTYMALIZOWANA KONFIGURACJA SYSTEMU CZĄSTECZEK DLA LEPSZEJ WYDAJNOŚCI
    const TEST_PARTICLE_COUNT = 300; // Zmniejszona liczba cząstek dla lepszej wydajności
    const particleSystem = new BABYLON.ParticleSystem("bloodCells", TEST_PARTICLE_COUNT, scene);

    // Używamy okrągłej tekstury dla krwinek czerwonych
    particleSystem.particleTexture = new BABYLON.Texture("textures/blood_cell_soft.png", scene);

    // Emiter sferyczny dla lepszego rozproszenia cząstek wokół gracza
    const sphereEmitter = particleSystem.createSphereEmitter(tunnelRadius * 0.8);
    sphereEmitter.radiusRange = 0.8; // 0-1, gdzie 1 oznacza pełny promień

    // Ustaw pozycję emitera
    particleSystem.emitter = emitterPosition;

    // Krwinki poruszają się w kierunku przeciwnym do ruchu gracza (gracz porusza się w -Z)
    particleSystem.direction1 = new BABYLON.Vector3(0, 0, -1);
    particleSystem.direction2 = new BABYLON.Vector3(0, 0, -1);

    // Zwiększona prędkość emisji dla lepszej widoczności
    particleSystem.minEmitPower = 0.8;
    particleSystem.maxEmitPower = 1.5;

    // Dłuższy czas życia cząstek
    particleSystem.minLifeTime = 3.0;
    particleSystem.maxLifeTime = 5.0;

    // Większe rozmiary cząstek dla lepszej widoczności
    particleSystem.minSize = tunnelRadius * 0.15;
    particleSystem.maxSize = tunnelRadius * 0.25;

    // Bardziej intensywne kolory z większą nieprzezroczystością
    particleSystem.color1 = new BABYLON.Color4(1, 0, 0, 1.0); // pełna czerwień, pełna nieprzezroczystość
    particleSystem.color2 = new BABYLON.Color4(1, 0.3, 0.3, 0.9);
    particleSystem.colorDead = new BABYLON.Color4(0.7, 0, 0, 0.3); // Bardziej widoczny kolor końcowy

    // Zmniejszona częstotliwość emisji dla lepszej wydajności
    particleSystem.emitRate = TEST_PARTICLE_COUNT * 1.5;

    // Tryb mieszania dla lepszej widoczności
    particleSystem.blendMode = BABYLON.ParticleSystem.BLENDMODE_ADD;

    // Brak grawitacji
    particleSystem.gravity = new BABYLON.Vector3(0, 0, 0);

    // Dodaj efekt wirowania cząstek
    particleSystem.minAngularSpeed = 0.1;
    particleSystem.maxAngularSpeed = 0.5;

    // Używamy równego skalowania dla okrągłych krwinek
    particleSystem.minScaleX = 1.0;
    particleSystem.maxScaleX = 1.2;
    particleSystem.minScaleY = 1.0;
    particleSystem.maxScaleY = 1.2;

    // Wyłącz billboarding aby obiekty były 3D zamiast płaskich
    particleSystem.isBillboardBased = false;

    // Ustaw wyższą grupę renderowania
    particleSystem.renderingGroupId = 1;

    // Uruchom system cząstek
    particleSystem.start();

    // Zapisz referencję globalnie
    window.bloodCellSystem = particleSystem;


    return particleSystem;
}

// --- AKTUALIZACJA EMITERA CZĄSTECZEK KRWINEK CZERWONYCH ---
function updateBloodCellParticleEmitter() {
    // Check if demo mode is active - if so, skip blood cell emitter update
    const isDemoMode = typeof window.isDemoActive === 'function' && window.isDemoActive();
    if (isDemoMode) {
        return;
    }

    if (window.bloodCellSystem && typeof bunnyCollider !== 'undefined' && bunnyCollider && bunnyCollider.position) {
        // Position the emitter close to the player (within sight)
        if (Array.isArray(tunnelSections) && tunnelSections.length > 0) {
            // Get the current player section
            const currentSection = findSectionAtPosition(bunnyCollider.position.z);

            if (currentSection && currentSection.centerPoint) {
                // Position the emitter further ahead of the player for better effect
                const emitterPosition = new BABYLON.Vector3(
                    currentSection.centerPoint.x,
                    currentSection.centerPoint.y,
                    bunnyCollider.position.z - 15 // 15 units ahead of the player
                );

                window.bloodCellSystem.emitter = emitterPosition;

                // Update particle system direction to flow toward the player
                const directionToPlayer = bunnyCollider.position.subtract(emitterPosition).normalize();
                window.bloodCellSystem.direction1 = directionToPlayer.add(new BABYLON.Vector3(-0.1, -0.1, 0));
                window.bloodCellSystem.direction2 = directionToPlayer.add(new BABYLON.Vector3(0.1, 0.1, 0));
            } else {
                // Fallback: Position further from the player
                window.bloodCellSystem.emitter = bunnyCollider.position.clone().add(new BABYLON.Vector3(0, 0, -15));
            }
        } else {
            // Fallback if no tunnel sections are available
            window.bloodCellSystem.emitter = bunnyCollider.position.clone().add(new BABYLON.Vector3(0, 0, -15));
        }
    }
}

// --- Deklaracje globalne ---

// Silnik i Scena
let engine = null;
let scene = null;
let canvas = null;
let renderingPipeline = null;

// Synchronize global variables with window objects
function syncGlobalVariables() {
    if (window.engine && !engine) {
        engine = window.engine;
    }
    if (window.scene && !scene) {
        scene = window.scene;
    }
}

// Call sync function periodically
if (typeof window !== 'undefined') {
    setInterval(syncGlobalVariables, 100);
}

// Gracz i Kamera
let bunny = null;
let bunnyCollider = null;
let camera = null;

// Świat
let tunnelMesh = null;
const tunnelSections = [];
let currentSectionIndex = 0;

// FFR Data
let tunnelFFRValues = []; // Przechowuje wartości FFR dla każdej sekcji tunelu
let tunnelFFRColors = []; // Przechowuje kolory FFR dla każdej sekcji tunelu

// Obiekty Gry
const eggs = [];
const obstacles = [];
const lasers = [];
const wallCubes = [];

// Stan Gry
let gameRunning = false;
let score = 0;
let energy = 100;
let ammo = 10;
let currentSpeedMultiplier = 1.0; // Start at 1.0, will be adjusted based on Config.MIN_SPEED_MULTIPLIER
let lastShotTime = 0;

let lastCollisionTime = 0;
let lastWallCubeCollisionTime = 0;
let wallCollisionCooldown = 0;

// Dźwięk
let audioContext = null;
// Default to true for visual effects
let effectsEnabled = true;
let musicEnabled = true;
let musicTrack = "random"; // "random", "music1", "music2", "music3", "music4"
let currentMusic = null;
let collectSound = null;
let collisionSound = null;
let destructionSound = null;
let laserSound = null;
let ricochetSound = null;
let collisionHeartbeatBuffer = null; // Declared here

// Dźwięki dla ataków krwinek białych
let attackSound = null;
let hitSound = null;

// Multiplayer Settings
let multiplayerEnabled = true; // Always enable multiplayer functionality
let allowJoining = true; // Allow other players to join the session
let playerUsername = ""; // Player's username for multiplayer

// Efekty Cząstek
let eggCollectParticles = null;
let obstacleCollisionParticles = null;
let laserHitParticles = null;

// Interwały Timers
let eggSpawnInterval = null;
let obstacleSpawnInterval = null;
let wallCubeSpawnInterval = null;
let redBloodCellSpawnInterval = null;
let speedIncreaseInterval = null;
let ammoRegenInterval = null;

// Input
var keysPressed = {};

// Hemolens logo
let logoPlane = null; // *** ADDED: For Hemolens Logo ***
let logoLight = null; // *** ADDED: For Hemolens Logo Light ***

// Elementy UI - zgrupowane w jednym obiekcie dla lepszej organizacji
const uiElements = {
    scoreDisplay: null,
    livesDisplay: null,
    energyDisplay: null,
    speedDisplay: null,
    ammoDisplay: null,
    finalScore: null,
    startBtn: null,
    restartBtn: null,
    nextLevelBtn: null,
    levelSelectBtn: null,
    levelSelectFromGameOverBtn: null,
    backToStartBtn: null,
    progressBar: null,
    diagnostics: null,
    loadingScreen: null,
    loadingText: null,
    startWindow: null,     // New start window
    startWindowLogo: null, // Logo in the start window
    startGameBtn: null,    // Start game button in start window
    settingsWindowBtn: null, // Settings button in start window
    infoWindowBtn: null,   // Info button in start window
    startScreen: null,
    gameOverScreen: null,
    levelListScreen: null,
    levelListContainer: null,
    soundControl: null,
    minimap: null,
    levelDisplay: null, // For Level UI
    introMessage: null, // For Intro Message
    infoBtn: null, // For Information Button
    infoScreen: null, // For Information Screen
    gameInfoContent: null, // For Game Information Content
    backFromInfoBtn: null, // For Back Button on Info Screen
    settingsBtn: null, // For Settings Button
    settingsScreen: null, // For Settings Screen
    effectsEnabledCheckbox: null, // For Effects Enabled Checkbox
    musicEnabledCheckbox: null, // For Music Enabled Checkbox
    musicTrackRadios: null, // For Music Track Radio Buttons
    saveSettingsBtn: null, // For Save Settings Button
    backFromSettingsBtn: null, // For Back Button on Settings Screen
    allowJoiningCheckbox: null, // For Allow Joining Checkbox
    playerUsernameInput: null, // For Player Username Input
};

// Elementy audio
let backgroundMusic = null;
let eggCollectSound = null;
let obstacleCollisionSound = null;
let gameOverSound = null;
let levelCompleteSound = null;

// Elementy świata
let tunnelLights = [];
let tunnelEndLogo = null;
// coronarySegments is already declared at the top of the file
// gameLevel is already declared and exported at the top of the file

const deviceOrientation = {
    alpha: 0, // Z rotation (yaw) - not typically used for tilt
    beta: 0,  // X rotation (pitch) - front/back tilt
    gamma: 0, // Y rotation (roll) - left/right tilt
    available: false, // Czy API jest dostępne
    permissionGranted: false // Czy użytkownik udzielił zgody (dla iOS)
};

let gameData = {}; // Dodaj to w sekcji globalnych zmiennych

let cameraReachedPlayer = false;

let soundEnabled = true;

// Export all global variables
const globals = {
    coronarySegments, atheroscleroticTypes,
    engine, scene, canvas, renderingPipeline,
    bunny, bunnyCollider, camera,
    tunnelMesh, tunnelSections, currentSectionIndex, tunnelLights, tunnelEndLogo,
    eggs, obstacles, lasers, wallCubes,
    gameRunning, score, energy, ammo, currentSpeedMultiplier,
    lastShotTime, lastCollisionTime, lastWallCubeCollisionTime, wallCollisionCooldown,
    audioContext, effectsEnabled, musicEnabled, musicTrack, currentMusic, backgroundMusic,
    collectSound, collisionSound, destructionSound, laserSound, ricochetSound, collisionHeartbeatBuffer, attackSound, hitSound,
    eggCollectSound, obstacleCollisionSound, gameOverSound, levelCompleteSound,
    multiplayerEnabled, allowJoining, playerUsername,
    eggCollectParticles, obstacleCollisionParticles, laserHitParticles,
    eggSpawnInterval, obstacleSpawnInterval, wallCubeSpawnInterval, redBloodCellSpawnInterval, speedIncreaseInterval, ammoRegenInterval,
    keysPressed,
    logoPlane, logoLight,
    uiElements
};


// Log loading only if in DEBUG mode (now safe to check)
if (DEBUG_MODE) {
    console.log("Globals declared (DEBUG MODE Active). DEBUG_MODE defined first.");
}

// Expose critical variables to window object for module scripts
window.tunnelSections = tunnelSections;
window.tunnelMesh = tunnelMesh;
window.currentSectionIndex = currentSectionIndex;
window.tunnelFFRValues = tunnelFFRValues;
window.tunnelFFRColors = tunnelFFRColors;
window.atheroscleroticTypes = atheroscleroticTypes;
window.coronarySegments = coronarySegments;

// --- END OF FILE globals.js ---
