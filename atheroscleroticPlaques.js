// atheroscleroticPlaques.js - System zmian miażdżycowych (3D klastry)

// Tablica przechowująca wszystkie zmiany miażdżycowe
let atheroscleroticPlaques = [];

// Zapamiętywanie napotkanych typów blaszek przez gracza
const encounteredPlaqueTypes = new Set();

// Tekstura plastra miodu dla zmian miażdżycowych
let honeycombTexture = null;

// Konfiguracja zmian miażdżycowych
const PLAQUE_CONFIG = {
    // Liczba klastrów zmian miażdżycowych na poziom trudności - zwiększono dla większej intensywności
    CLUSTERS_PER_LEVEL: {
        EASY: 5,    // Zwiększono z 3 do 5 (Poziomy 1-6)
        MEDIUM: 8,  // Zwiększono z 5 do 8 (Poziomy 7-12)
        HARD: 12    // Zwiększono z 8 do 12 (Poziomy 13-18)
    },

    // Liczba zmian w klastrze - zwiększono dla większej intensywności
    PLAQUES_PER_CLUSTER: {
        MIN: 4,     // Zwiększono z 3 do 4
        MAX: 12     // Zwiększono z 8 do 12
    },

    // Rozmiary zmian miażdżycowych
    SIZE: {
        MIN: 0.08,
        MAX: 0.25
    },

    // Kolory dla różnych typów zmian miażdżycowych
    COLORS: {
        // Zmiany zwapniałe (białe/szare)
        CALCIFIED: {
            BASE: new BABYLON.Color3(0.9, 0.9, 0.9),
            EMISSION: new BABYLON.Color3(0.6, 0.6, 0.7),
            DAMAGE: 15
        },
        // Zmiany lipidowe (żółte/pomarańczowe)
        LIPID: {
            BASE: new BABYLON.Color3(0.9, 0.7, 0.2),
            EMISSION: new BABYLON.Color3(0.8, 0.5, 0.1),
            DAMAGE: 10
        },
        // Zmiany włókniste (czerwonawe)
        FIBROUS: {
            BASE: new BABYLON.Color3(0.8, 0.3, 0.3),
            EMISSION: new BABYLON.Color3(0.6, 0.2, 0.2),
            DAMAGE: 8
        }
    },

    // Prawdopodobieństwo wystąpienia poszczególnych typów zmian
    TYPE_PROBABILITY: {
        CALCIFIED: 0.3,
        LIPID: 0.4,
        FIBROUS: 0.3
    },

    // Odległość od ściany tunelu (0 = na ścianie, 1 = w centrum)
    WALL_DISTANCE: {
        MIN: 0.05,
        MAX: 0.15
    },

    // Parametry kolizji
    COLLISION: {
        COOLDOWN: 300,  // ms między kolizjami
        DAMAGE_MULTIPLIER: 1.2  // mnożnik obrażeń
    }
};

// Klasyfikacja AHA – typy blaszek miażdżycowych
const PLAQUE_TYPES = [
  {
    type: 1,
    name: "Initial lesion",
    size: [0.4, 0.7],
    destructible: true,
    color: "#e0e0e0",
    texture: "textures/type1.jpg",
    description: "Wczesne złogi cholesterolu, łatwe do usunięcia.",
    visualHint: "Mała, półprzezroczysta plamka",
    gameplayNote: "Shoot to destroy",
    hitPoints: 1
  },
  {
    type: 2,
    name: "Fatty streak",
    size: [0.5, 0.8],
    destructible: true,
    color: "#ffe066",
    texture: "textures/type2.jpg",
    description: "Żółtawa smuga, umiarkowanie zwęża światło naczynia.",
    visualHint: "Podłużna, żółtawa smuga",
    gameplayNote: "Shoot to destroy",
    hitPoints: 1
  },
  {
    type: 3,
    name: "Intermediate lesion",
    size: [0.6, 0.9],
    destructible: true,
    color: "#ffd166",
    texture: "textures/type3.jpg",
    description: "Miękka, lekko wystająca blaszka, spowalnia gracza.",
    visualHint: "Wybrzuszona, lekko uniesiona plama",
    gameplayNote: "Shoot to destroy",
    hitPoints: 2
  },
  {
    type: 4,
    name: "Atheroma",
    size: [0.7, 1.0],
    destructible: false,
    color: "#ff8c42",
    texture: "textures/type4.jpg",
    description: "Wyraźna, miękka blaszka – nie do zniszczenia.",
    visualHint: "Wyraźne, miękkie wybrzuszenie",
    gameplayNote: "Must be avoided",
    damage: 15
  },
  {
    type: 5,
    name: "Fibroatheroma",
    size: [0.8, 1.1],
    destructible: false,
    color: "#bdbdbd",
    texture: "textures/type5.jpg",
    description: "Twarda, często zwapniała blaszka – odbija gracza.",
    visualHint: "Twardy, biały lub zwapniały guzek",
    gameplayNote: "Must be avoided",
    damage: 20
  },
  {
    type: 6,
    name: "Complicated lesion",
    size: [0.9, 1.2],
    destructible: false,
    color: "#d7263d",
    texture: "textures/type6.jpg",
    description: "Nieregularna, z zakrzepem – najgroźniejsza.",
    visualHint: "Duża, nieregularna z widocznym skrzepliną",
    gameplayNote: "Must be avoided",
    damage: 30
  }
];

// Ostatni czas kolizji ze zmianą miażdżycową
let lastPlaqueCollisionTime = 0;

/**
 * Generuje zmiany miażdżycowe wzdłuż tunelu
 * @param {Array} tunnelSections - Sekcje tunelu
 * @param {number} gameLevel - Aktualny poziom gry
 * @param {BABYLON.Scene} scene - Scena Babylon.js
 */
function generateAtheroscleroticPlaques(tunnelSections, gameLevel, scene) {
    if (!tunnelSections || tunnelSections.length === 0 || !scene) {
        console.error("Nie można wygenerować zmian miażdżycowych - brak sekcji tunelu lub sceny");
        return;
    }

    // Wyczyść istniejące zmiany miażdżycowe
    clearAtheroscleroticPlaques();

    // Określ liczbę klastrów na podstawie poziomu trudności
    let clustersCount;
    if (gameLevel <= 6) {
        clustersCount = PLAQUE_CONFIG.CLUSTERS_PER_LEVEL.EASY;
    } else if (gameLevel <= 12) {
        clustersCount = PLAQUE_CONFIG.CLUSTERS_PER_LEVEL.MEDIUM;
    } else {
        clustersCount = PLAQUE_CONFIG.CLUSTERS_PER_LEVEL.HARD;
    }

    console.log(`Generowanie ${clustersCount} klastrów zmian miażdżycowych dla poziomu ${gameLevel}`);

    // Wybierz losowe sekcje tunelu dla klastrów (z wyłączeniem pierwszych i ostatnich 10%)
    const validSectionIndices = [];
    const startExcludeCount = Math.floor(tunnelSections.length * 0.1);
    const endExcludeCount = Math.floor(tunnelSections.length * 0.1);

    for (let i = startExcludeCount; i < tunnelSections.length - endExcludeCount; i++) {
        validSectionIndices.push(i);
    }

    // Wymieszaj indeksy, aby uzyskać losowe rozmieszczenie
    shuffleArray(validSectionIndices);

    // Wybierz sekcje dla klastrów
    const clusterSectionIndices = validSectionIndices.slice(0, clustersCount);

    // Generuj klastry zmian miażdżycowych
    for (let i = 0; i < clusterSectionIndices.length; i++) {
        const sectionIndex = clusterSectionIndices[i];
        const section = tunnelSections[sectionIndex];

        // Określ liczbę zmian w klastrze
        const plaquesInCluster = Math.floor(
            PLAQUE_CONFIG.PLAQUES_PER_CLUSTER.MIN +
            Math.random() * (PLAQUE_CONFIG.PLAQUES_PER_CLUSTER.MAX - PLAQUE_CONFIG.PLAQUES_PER_CLUSTER.MIN + 1)
        );

        // Generuj zmiany w klastrze
        generatePlaqueCluster(section, plaquesInCluster, scene);
    }

    console.log(`Wygenerowano ${atheroscleroticPlaques.length} zmian miażdżycowych w ${clustersCount} klastrach`);
}

/**
 * Generuje klaster zmian miażdżycowych w określonej sekcji tunelu
 * @param {Object} section - Sekcja tunelu
 * @param {number} plaquesCount - Liczba zmian w klastrze
 * @param {BABYLON.Scene} scene - Scena Babylon.js
 */
function generatePlaqueCluster(section, plaquesCount, scene) {
    if (!section || !section.centerPoint || !section.radius) {
        console.warn("Nieprawidłowa sekcja tunelu dla klastra zmian miażdżycowych");
        return;
    }

    // Wybierz losowy kąt bazowy dla klastra (w radianach)
    const baseAngle = Math.random() * Math.PI * 2;

    // Wybierz losowy zakres kątowy dla klastra (30-90 stopni)
    const angleRange = (Math.PI / 6) + (Math.random() * Math.PI / 3);

    // Generuj zmiany miażdżycowe w klastrze
    for (let i = 0; i < plaquesCount; i++) {
        // Losowy kąt w zakresie klastra
        const angle = baseAngle + (Math.random() * angleRange - angleRange / 2);

        // Losowy rozmiar zmiany (diameter)
        const plaqueDiameter = PLAQUE_CONFIG.SIZE.MIN +
            Math.random() * (PLAQUE_CONFIG.SIZE.MAX - PLAQUE_CONFIG.SIZE.MIN);

        // Określ efektywny promień klastra, który uwzględnia jego wewnętrzne rozproszenie.
        // Jeśli size to średnica, a w createPlaque maksymalny zasięg to 0.7 * maxClusterRadius,
        // to efektywny promień to 0.7 * plaqueDiameter/2.
        const effectivePlaqueRadius = plaqueDiameter * 0.7 / 2; // Poprawiony współczynnik zgodny z maxSphereOffsetFromCenter w createPlaque.

        // Ile procent efektywnego promienia blaszki ma być "zanurzone" w ścianie.
        // WALL_DISTANCE.MIN do MAX (0.05 do 0.15)
        // Jeśli 0.05, to 95% efektywnego promienia wystaje z tunelu.
        // Jeśli 0.15, to 85% efektywnego promienia wystaje z tunelu.
        const embeddingFraction = PLAQUE_CONFIG.WALL_DISTANCE.MIN +
            Math.random() * (PLAQUE_CONFIG.WALL_DISTANCE.MAX - PLAQUE_CONFIG.WALL_DISTANCE.MIN);

        // Oblicz odległość centrum blaszki od centrum tunelu.
        // section.radius (wewnętrzny promień tunelu) - (część efektywnego promienia blaszki, która ma wystawać)
        const distanceFromCenter = section.radius - (effectivePlaqueRadius * (1 - embeddingFraction));

        const x = section.centerPoint.x + Math.cos(angle) * distanceFromCenter;
        const y = section.centerPoint.y + Math.sin(angle) * distanceFromCenter;
        // Dodaj losowy offset w osi Z dla efektu 3D (±40% promienia)
        const zOffset = (Math.random() * 2 - 1) * section.radius * 0.4;
        const z = section.centerPoint.z + zOffset;

        // Losowy typ zmiany miażdżycowej
        const plaqueType = getRandomPlaqueType();

        // Utwórz zmianę miażdżycową
        createPlaque(new BABYLON.Vector3(x, y, z), plaqueDiameter, plaqueType, scene);
    }
}

/**
 * Tworzy pojedynczą zmianę miażdżycową
 * @param {BABYLON.Vector3} position - Pozycja zmiany
 * @param {number} size - Rozmiar zmiany
 * @param {string} type - Typ zmiany (CALCIFIED, LIPID, FIBROUS)
 * @param {BABYLON.Scene} scene - Scena Babylon.js
 */
function createPlaque(position, size, type, scene) {
    // Określ typ AHA na podstawie typu zmiany
    let ahaType;
    if (type === 'CALCIFIED') {
        ahaType = Math.random() < 0.7 ? 5 : 6;
    } else if (type === 'LIPID') {
        ahaType = Math.random() < 0.6 ? 2 : (Math.random() < 0.5 ? 1 : 3);
    } else {
        ahaType = Math.random() < 0.6 ? 3 : (Math.random() < 0.5 ? 4 : 5);
    }
    const ahaTypeInfo = PLAQUE_TYPES.find(t => t.type === ahaType);

    // --- KLASTER 3D: generuj nieregularny klaster w pełni trójwymiarowy ---
    const cluster = new BABYLON.TransformNode(`plaqueCluster_${type}_${atheroscleroticPlaques.length}`, scene);
    const spheresCount = 8 + Math.floor(Math.random() * 5); // 8-12 sfer dla bardziej złożonego klastra

    // Określamy maksymalny promień rozrzutu sfer w klastrze,
    // tak aby cały klaster mieścił się w zadanym 'size' (średnicy).
    // Jeśli size to średnica, to size/2 to promień.
    const maxClusterRadius = size / 2; // Rzeczywisty promień, w którym ma się zmieścić klaster
    const maxSphereOffsetFromCenter = maxClusterRadius * 0.7; // Maksymalny offset centrum sfery od centrum klastra
    const maxIndividualSphereRadius = maxClusterRadius * 0.35; // Zmniejszony maksymalny promień pojedynczej sfery dla lepszej kontroli

    for (let i = 0; i < spheresCount; i++) {
        // Bardziej kontrolowany rozmiar pojedynczej sfery (średnica)
        // Mniejszy zakres losowości dla bardziej przewidywalnych rozmiarów
        const individualSphereDiameter = maxClusterRadius * (0.4 + Math.random() * 0.4);

        // Losowy offset sfery od centrum klastra
        const phi = Math.random() * Math.PI * 2; // Azymut (0-2π)
        const theta = Math.random() * Math.PI; // Elewacja (0-π)
        // Offset sfery od centrum klastra, max 0.7 promienia klastra
        const radiusOffset = maxSphereOffsetFromCenter * Math.random();

        // Konwersja na kartezjańskie
        const offsetX = radiusOffset * Math.sin(theta) * Math.cos(phi);
        const offsetY = radiusOffset * Math.sin(theta) * Math.sin(phi);
        const offsetZ = radiusOffset * Math.cos(theta);

        // Tworzenie różnorodnych 3D kształtów dla lepszego efektu wizualnego
        let sphere;
        const shapeType = Math.random();

        if (shapeType < 0.25) {
            // Dodecahedron - wielościan o 12 ścianach
            sphere = BABYLON.MeshBuilder.CreatePolyhedron(
                `plaquePolyhedron_${type}_${atheroscleroticPlaques.length}_${i}`,
                { type: 2, size: individualSphereDiameter / 2 },
                scene
            );
        } else if (shapeType < 0.4) {
            // Icosahedron - wielościan o 20 ścianach
            sphere = BABYLON.MeshBuilder.CreatePolyhedron(
                `plaqueIcosahedron_${type}_${atheroscleroticPlaques.length}_${i}`,
                { type: 1, size: individualSphereDiameter / 2 },
                scene
            );
        } else if (shapeType < 0.55) {
            // Octahedron - wielościan o 8 ścianach
            sphere = BABYLON.MeshBuilder.CreatePolyhedron(
                `plaqueOctahedron_${type}_${atheroscleroticPlaques.length}_${i}`,
                { type: 0, size: individualSphereDiameter / 2 },
                scene
            );
        } else if (shapeType < 0.75) {
            // Deformowana sfera z większą liczbą segmentów
            sphere = BABYLON.MeshBuilder.CreateSphere(
                `plaqueSphere_${type}_${atheroscleroticPlaques.length}_${i}`,
                { diameter: individualSphereDiameter, segments: 32 },
                scene
            );

            // Dodaj losową deformację dla bardziej organicznego wyglądu
            const positions = sphere.getVerticesData(BABYLON.VertexBuffer.PositionKind);
            for (let v = 0; v < positions.length; v += 3) {
                const deformFactor = 0.2 + Math.random() * 0.3; // 20-50% deformacji
                positions[v] *= (1 + (Math.random() - 0.5) * deformFactor);
                positions[v + 1] *= (1 + (Math.random() - 0.5) * deformFactor);
                positions[v + 2] *= (1 + (Math.random() - 0.5) * deformFactor);
            }
            sphere.setVerticesData(BABYLON.VertexBuffer.PositionKind, positions);
            sphere.createNormals(true);
        } else {
            // Elipsoida - bardziej organiczny kształt z większą różnorodnością
            sphere = BABYLON.MeshBuilder.CreateSphere(
                `plaqueEllipsoid_${type}_${atheroscleroticPlaques.length}_${i}`,
                { diameter: individualSphereDiameter, segments: 24 },
                scene
            );

            // Skalowanie w różnych osiach dla efektu elipsoidy - PEŁNE 3D
            const scaleX = 0.6 + Math.random() * 0.8; // 0.6-1.4
            const scaleY = 0.6 + Math.random() * 0.8; // 0.6-1.4
            const scaleZ = 0.6 + Math.random() * 0.8; // 0.6-1.4 (równorzędne z X i Y)
            sphere.scaling = new BABYLON.Vector3(scaleX, scaleY, scaleZ);
        }

        sphere.parent = cluster;
        sphere.position = new BABYLON.Vector3(offsetX, offsetY, offsetZ);

        // Skalowanie pojedynczej sfery - PEŁNE 3D bez preferowania żadnej osi
        const scaleX = 0.7 + Math.random() * 0.6; // Zakres 0.7 - 1.3
        const scaleY = 0.7 + Math.random() * 0.6; // Zakres 0.7 - 1.3
        const scaleZ = 0.7 + Math.random() * 0.6; // Zakres 0.7 - 1.3 (równorzędne z X i Y)
        sphere.scaling.x = scaleX;
        sphere.scaling.y = scaleY;
        sphere.scaling.z = scaleZ;

        // Dodaj lokalną rotację każdej sfery dla większej różnorodności
        sphere.rotation.x = Math.random() * Math.PI * 2;
        sphere.rotation.y = Math.random() * Math.PI * 2;
        sphere.rotation.z = Math.random() * Math.PI * 2;

        // Deformacja wierzchołków - zwiększ siłę
        const positions = sphere.getVerticesData(BABYLON.VertexBuffer.PositionKind);
        if (positions) {
            const deformedPositions = [];

            // Oblicz środek geometryczny dla lepszej deformacji
            let centerX = 0, centerY = 0, centerZ = 0;
            let vertexCount = positions.length / 3;
            for (let j = 0; j < positions.length; j += 3) {
                centerX += positions[j];
                centerY += positions[j + 1];
                centerZ += positions[j + 2];
            }
            centerX /= vertexCount;
            centerY /= vertexCount;
            centerZ /= vertexCount;

            // Zastosuj bardziej złożoną deformację zależną od pozycji wierzchołka
            for (let j = 0; j < positions.length; j += 3) {
                // Oblicz wektor od środka do wierzchołka
                const vx = positions[j] - centerX;
                const vy = positions[j + 1] - centerY;
                const vz = positions[j + 2] - centerZ;

                // Oblicz odległość od środka (znormalizowana)
                const dist = Math.sqrt(vx*vx + vy*vy + vz*vz);

                // Bardziej agresywna i nieregularna deformacja
                const noiseFactor = 0.8; // Zwiększ na 0.8 dla mocniejszej deformacji (było 0.6)
                const noiseX = 1 + (Math.random() - 0.5) * noiseFactor;
                const noiseY = 1 + (Math.random() - 0.5) * noiseFactor;
                const noiseZ = 1 + (Math.random() - 0.5) * noiseFactor;

                // Większa deformacja dla wierzchołków dalej od środka
                const distFactor = 1 + (dist * 0.3);

                deformedPositions.push(positions[j] * noiseX * distFactor);
                deformedPositions.push(positions[j + 1] * noiseY * distFactor);
                deformedPositions.push(positions[j + 2] * noiseZ * distFactor);
            }

            // Aktualizuj geometrię i przelicz normalne
            sphere.updateVerticesData(BABYLON.VertexBuffer.PositionKind, deformedPositions);
            sphere.createNormals(true);

            // Opcjonalnie: dodaj dodatkowe wygładzanie dla bardziej organicznego wyglądu
            if (sphere.geometry) {
                sphere.forceSharedVertices();
            }
        }

        // Materiał z zaawansowanymi efektami 3D
        const mat = new BABYLON.StandardMaterial(`plaqueMat_${type}_${atheroscleroticPlaques.length}_${i}`, scene);

        // Podstawowe ustawienia materiału
        if (ahaTypeInfo && ahaTypeInfo.texture) {
            mat.diffuseTexture = new BABYLON.Texture(ahaTypeInfo.texture, scene);
        } else {
            const colorConfig = PLAQUE_CONFIG.COLORS[type] || {
                BASE: new BABYLON.Color3(0.7, 0.7, 0.7),
                EMISSION: new BABYLON.Color3(0.3, 0.3, 0.3),
                DAMAGE: 10
            };
            mat.diffuseColor = colorConfig.BASE;
            mat.emissiveColor = colorConfig.EMISSION;
        }

        // Zaawansowane efekty 3D dla wszystkich materiałów

        // 1. Specular - odblaski świetlne dla lepszego efektu 3D
        mat.specularColor = new BABYLON.Color3(0.6, 0.6, 0.6); // Zwiększone odblaski
        mat.specularPower = 32; // Mniejsza wartość = szersze odblaski

        // 2. Normal mapping (bump) - dodaje szczegóły powierzchni
        try {
            mat.bumpTexture = new BABYLON.Texture("textures/others_0003_normal_directx_1k.png", scene);
            mat.bumpTexture.level = 1.0; // Zwiększony poziom dla lepszego efektu 3D
        } catch (e) {
            console.warn("Nie można załadować normal texture");
        }

        // 3. Ambient Occlusion - podkreśla zagłębienia i szczeliny
        try {
            mat.ambientTexture = new BABYLON.Texture("textures/others_0003_ao_1k.jpg", scene);
            mat.ambientColor = new BABYLON.Color3(1, 1, 1); // Pełna intensywność AO
        } catch (e) {
            console.warn("Nie można załadować ambient occlusion texture");
        }

        // 4. Roughness - kontroluje mikrodetale powierzchni
        if (!ahaTypeInfo || !ahaTypeInfo.texture) {
            try {
                // Użyj roughness tylko dla materiałów bez tekstury
                mat.specularTexture = new BABYLON.Texture("textures/others_0003_roughness_1k.jpg", scene);
            } catch (e) {
                console.warn("Nie można załadować roughness texture");
            }
        }

        // 5. Fresnel effect - dodaje efekt krawędziowy dla lepszego wrażenia 3D
        mat.useReflectionFresnelFromSpecular = true;
        mat.reflectionFresnelParameters = new BABYLON.FresnelParameters();
        mat.reflectionFresnelParameters.bias = 0.02;
        mat.reflectionFresnelParameters.power = 2.5;
        mat.reflectionFresnelParameters.leftColor = BABYLON.Color3.White();
        mat.reflectionFresnelParameters.rightColor = BABYLON.Color3.Black();

        // Dodatkowe właściwości 3D
        mat.backFaceCulling = false; // Renderuj obie strony dla lepszego efektu 3D
        mat.twoSidedLighting = true; // Oświetlenie z obu stron

        sphere.material = mat;

        // Wymuś renderowanie jako pełny 3D obiekt
        sphere.receiveShadows = true;
        sphere.checkCollisions = true;
        sphere.isPickable = true;
    }

    // Pozycjonowanie i rotacja klastra
    cluster.position = position;

    // Dodaj losową rotację całego klastra dla jeszcze większej różnorodności
    cluster.rotation = new BABYLON.Vector3(
        Math.random() * Math.PI * 2,
        Math.random() * Math.PI * 2,
        Math.random() * Math.PI * 2
    );

    // Dodaj losowe przesunięcie dla większej różnorodności
    // Małe przesunięcie od oryginalnej pozycji dla bardziej naturalnego wyglądu
    const positionOffset = new BABYLON.Vector3(
        (Math.random() - 0.5) * 0.05,
        (Math.random() - 0.5) * 0.05,
        (Math.random() - 0.5) * 0.05
    );
    cluster.position.addInPlace(positionOffset);

    // Dodaj losowe skalowanie całego klastra - PEŁNE 3D bez spłaszczania
    const clusterScaleX = 0.8 + Math.random() * 0.4; // 0.8-1.2 dla X
    const clusterScaleY = 0.8 + Math.random() * 0.4; // 0.8-1.2 dla Y
    const clusterScaleZ = 0.8 + Math.random() * 0.4; // 0.8-1.2 dla Z (BEZ spłaszczania)
    cluster.scaling = new BABYLON.Vector3(clusterScaleX, clusterScaleY, clusterScaleZ);

    // Zapisz info o klastrze
    atheroscleroticPlaques.push({
        mesh: cluster,
        type: type,
        ahaType: ahaType,
        damage: (ahaTypeInfo && ahaTypeInfo.damage) ? ahaTypeInfo.damage : 10,
        size: size,
        originalPosition: position.clone(),
        destructible: ahaTypeInfo ? ahaTypeInfo.destructible : false,
        hitPoints: ahaTypeInfo && ahaTypeInfo.hitPoints ? ahaTypeInfo.hitPoints : 0,
        currentHitPoints: ahaTypeInfo && ahaTypeInfo.hitPoints ? ahaTypeInfo.hitPoints : 0
    });
}

/**
 * Zwraca losowy typ zmiany miażdżycowej na podstawie zdefiniowanych prawdopodobieństw
 * @returns {string} Typ zmiany (CALCIFIED, LIPID, FIBROUS)
 */
function getRandomPlaqueType() {
    const rand = Math.random();
    if (rand < PLAQUE_CONFIG.TYPE_PROBABILITY.CALCIFIED) {
        return 'CALCIFIED';
    } else if (rand < PLAQUE_CONFIG.TYPE_PROBABILITY.CALCIFIED + PLAQUE_CONFIG.TYPE_PROBABILITY.LIPID) {
        return 'LIPID';
    } else {
        return 'FIBROUS';
    }
}

/**
 * Wywołuje komunikat edukacyjny o typie blaszki
 * @param {number} typeNum - Numer typu blaszki
 */
function showPlaqueTypeInfo(typeNum) {
    const typeObj = PLAQUE_TYPES.find(t => t.type === typeNum);
    if (!typeObj) return;
    if (window.ui && typeof window.ui.showPlaqueTypeMessage === 'function') {
        window.ui.showPlaqueTypeMessage(
            typeObj.name,
            typeObj.description,
            `${typeObj.visualHint} | ${typeObj.destructible ? 'Można zniszczyć strzałem' : 'Nie do zniszczenia, należy omijać'}`
        );
    } else {
        // Fallback: jeśli UI nie jest gotowe, nie pokazuj alertu
    }
}

/**
 * Pomocnicza: zamiana typu tekstowego na numer AHA
 * @param {string} typeStr - Typ zmiany jako tekst
 * @returns {number} Numer typu zmiany
 */
function getPlaqueTypeNumber(typeStr) {
    switch(typeStr) {
        case 'CALCIFIED': return 5;
        case 'LIPID': return 2;
        case 'FIBROUS': return 3;
        default: {
            const found = PLAQUE_TYPES.find(t => t.type && t.type.toString() === typeStr.toString());
            return found ? found.type : 0;
        }
    }
}

/**
 * Aktualizuje zmiany miażdżycowe (animacje, efekty)
 * @param {number} deltaTime - Czas od ostatniej klatki w sekundach
 */
function updateAtheroscleroticPlaques(deltaTime) {
    // Flaga kontrolująca animację (domyślnie włączona)
    if (typeof window.animationEnabled === 'undefined') {
        window.animationEnabled = true;
    }

    // Jeśli animacja jest wyłączona, nie aktualizuj
    if (!window.animationEnabled) return;

    const now = performance.now();

    for (let i = 0; i < atheroscleroticPlaques.length; i++) {
        const plaque = atheroscleroticPlaques[i];
        if (!plaque.mesh || plaque.mesh.isDisposed()) continue;

        // Inicjalizuj parametry animacji, jeśli nie istnieją
        if (!plaque.animParams) {
            plaque.animParams = {
                rotationSpeed: 0.0005 + Math.random() * 0.001,
                pulseFreq: 0.0005 + Math.random() * 0.001,
                pulseAmp: 0.03 + Math.random() * 0.04,
                wobbleFreq: 0.0007 + Math.random() * 0.0006,
                wobbleAmp: 0.03 + Math.random() * 0.03,
                phaseOffset: Math.random() * Math.PI * 2,
                originalScale: plaque.mesh.scaling.clone()
            };
        }

        // Zaawansowana animacja 3D dla wszystkich typów blaszek

        // 1. Płynna rotacja całego klastra
        if (plaque.mesh.rotationQuaternion) {
            // Jeśli używamy quaternionów, zastosuj płynną rotację
            const rotAngle = now * plaque.animParams.rotationSpeed;
            const newRotation = BABYLON.Quaternion.RotationAxis(
                new BABYLON.Vector3(0, 1, 0),
                rotAngle
            );
            // Zachowaj oryginalną rotację i dodaj nową
            if (!plaque.originalRotation) {
                plaque.originalRotation = plaque.mesh.rotationQuaternion.clone();
            }
            plaque.mesh.rotationQuaternion = plaque.originalRotation.multiply(newRotation);
        } else {
            // Fallback dla standardowej rotacji
            plaque.mesh.rotation.y += deltaTime * plaque.animParams.rotationSpeed * 20;
        }

        // 2. Organiczne falowanie pozycji
        if (plaque.originalPosition) {
            // Złożony ruch falowy w 3D
            const timeOffset = now * plaque.animParams.wobbleFreq + plaque.animParams.phaseOffset;
            plaque.mesh.position.x = plaque.originalPosition.x + Math.sin(timeOffset) * plaque.animParams.wobbleAmp;
            plaque.mesh.position.y = plaque.originalPosition.y + Math.cos(timeOffset * 1.3) * plaque.animParams.wobbleAmp;
            plaque.mesh.position.z = plaque.originalPosition.z + Math.sin(timeOffset * 0.7) * plaque.animParams.wobbleAmp;
        }

        // 3. Typ-specyficzne animacje z ulepszonymi efektami 3D
        if (plaque.type === 'LIPID') {
            // Dla zmian lipidowych - organiczne pulsowanie w 3D
            const pulseFactor = Math.sin(now * plaque.animParams.pulseFreq) * plaque.animParams.pulseAmp + 1;

            // Asymetryczne pulsowanie dla lepszego efektu 3D
            plaque.mesh.scaling.x = plaque.animParams.originalScale.x * (pulseFactor * 0.9);
            plaque.mesh.scaling.y = plaque.animParams.originalScale.y * (pulseFactor * 1.1);
            plaque.mesh.scaling.z = plaque.animParams.originalScale.z * pulseFactor;

            // Dodatkowa animacja dzieci (sfer w klastrze)
            const children = plaque.mesh.getChildren();
            for (let j = 0; j < children.length; j++) {
                const child = children[j];
                if (child.scaling) {
                    // Każde dziecko pulsuje nieco inaczej
                    const childPulse = Math.sin(now * plaque.animParams.pulseFreq + j) * 0.03 + 1;
                    child.scaling.x *= childPulse;
                    child.scaling.y *= childPulse;
                    child.scaling.z *= childPulse;
                }
            }
        }
        else if (plaque.type === 'CALCIFIED') {
            // Dla zmian zwapniałych - powolna, nieregularna rotacja
            // Animuj dzieci (sfery/wielościany w klastrze)
            const children = plaque.mesh.getChildren();
            for (let j = 0; j < children.length; j++) {
                const child = children[j];
                if (child.rotation) {
                    // Każde dziecko obraca się nieco inaczej
                    child.rotation.x += deltaTime * (0.01 + j * 0.005);
                    child.rotation.z += deltaTime * (0.015 - j * 0.002);
                }
            }
        }
        else if (plaque.type === 'FIBROUS') {
            // Dla zmian włóknistych - delikatne, złożone drgania
            // Zastosuj efekt fali przechodzący przez klaster
            const children = plaque.mesh.getChildren();
            for (let j = 0; j < children.length; j++) {
                const child = children[j];
                if (child.position) {
                    // Efekt fali przesuwającej się przez klaster
                    const waveOffset = now * 0.001 + j * 0.5;
                    const waveAmp = 0.005 + (j % 3) * 0.002;

                    // Zachowaj oryginalną pozycję dziecka
                    if (!child.originalPosition) {
                        child.originalPosition = child.position.clone();
                    }

                    // Zastosuj ruch falowy
                    child.position.x = child.originalPosition.x + Math.sin(waveOffset) * waveAmp;
                    child.position.y = child.originalPosition.y + Math.cos(waveOffset * 1.2) * waveAmp;
                    child.position.z = child.originalPosition.z + Math.sin(waveOffset * 0.8) * waveAmp;
                }
            }
        }
    }
}

/**
 * Przełącza animację zmian miażdżycowych
 * @returns {boolean} Nowy stan animacji
 */
function toggleAnimation() {
    window.animationEnabled = !window.animationEnabled;
    console.log(`Animacja ${window.animationEnabled ? 'włączona' : 'wyłączona'}`);
    return window.animationEnabled;
}

/**
 * Sprawdza kolizje gracza ze zmianami miażdżycowymi
 * @param {BABYLON.AbstractMesh} playerMesh - Mesh gracza (bunnyCollider)
 * @returns {number} Obrażenia zadane graczowi (0 jeśli brak kolizji)
 */
function checkPlaqueCollisions(playerMesh) {
    if (!playerMesh || !Array.isArray(atheroscleroticPlaques)) return 0;

    const now = Date.now();
    // Sprawdź cooldown kolizji
    if (now - lastPlaqueCollisionTime < PLAQUE_CONFIG.COLLISION.COOLDOWN) {
        return 0;
    }

    for (let i = 0; i < atheroscleroticPlaques.length; i++) {
        const plaque = atheroscleroticPlaques[i];
        if (!plaque.mesh || plaque.mesh.isDisposed()) continue;

        // Sprawdź kolizję
        let collision = improvedPlaqueCollisionDetection(playerMesh, plaque);

        if (collision) {
            // Aktualizuj czas ostatniej kolizji
            lastPlaqueCollisionTime = now;

            // Wyświetl info o typie blaszki przy pierwszym kontakcie
            const typeNum = plaque.ahaType || getPlaqueTypeNumber(plaque.type);
            if (!encounteredPlaqueTypes.has(typeNum)) {
                encounteredPlaqueTypes.add(typeNum);
                showPlaqueTypeInfo(typeNum);
            }

            // Znajdź informacje o typie AHA
            const ahaTypeInfo = PLAQUE_TYPES.find(t => t.type === typeNum);

            // Oblicz obrażenia
            let damage = 0;
            if (ahaTypeInfo && ahaTypeInfo.damage) {
                damage = ahaTypeInfo.damage * PLAQUE_CONFIG.COLLISION.DAMAGE_MULTIPLIER;
            } else {
                damage = plaque.damage * PLAQUE_CONFIG.COLLISION.DAMAGE_MULTIPLIER;
            }

            // Efekty kolizji
            if (typeof playParticleEffect === 'function') {
                playParticleEffect("obstacleCollision", playerMesh.position);
            }

            // Dodaj efekt odrzutu dla zmiany miażdżycowej
            const impulseDirection = plaque.mesh.position.subtract(playerMesh.position).normalize();
            plaque.mesh.position.addInPlace(impulseDirection.scale(0.05));

            console.log(`Kolizja ze zmianą miażdżycową typu AHA ${typeNum} (${plaque.type}). Obrażenia: ${damage.toFixed(1)}`);

            return damage;
        }
    }

    return 0;
}

/**
 * Czyści wszystkie zmiany miażdżycowe
 */
function clearAtheroscleroticPlaques() {
    for (let i = 0; i < atheroscleroticPlaques.length; i++) {
        const plaque = atheroscleroticPlaques[i];
        if (plaque.mesh && !plaque.mesh.isDisposed()) {
            // Usuń efekt świecenia, jeśli istnieje
            const glowLayer = plaque.mesh.getScene().getGlowLayerByName(`plaqueGlow_${i}`);
            if (glowLayer) {
                glowLayer.dispose();
            }

            // Usuń materiał
            if (plaque.mesh.material) {
                plaque.mesh.material.dispose();
            }

            // Usuń mesh
            plaque.mesh.dispose();
        }
    }

    // Wyczyść tablicę
    atheroscleroticPlaques = [];
    console.log("Wyczyszczono wszystkie zmiany miażdżycowe");
}

/**
 * Pomocnicza funkcja do mieszania tablicy (algorytm Fisher-Yates)
 * @param {Array} array - Tablica do wymieszania
 */
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
}

/**
 * Sprawdza kolizje laserów ze zmianami miażdżycowymi
 * @returns {void}
 */
function checkLaserPlaqueCollisions() {
    // Sprawdź czy wszystkie wymagane zmienne są dostępne
    if (!Array.isArray(lasers) || !Array.isArray(atheroscleroticPlaques)) return;
    if (typeof scene === 'undefined' || !scene) return;
    if (typeof gameRunning === 'undefined' || !gameRunning) return;

    for (let i = lasers.length - 1; i >= 0; i--) {
        const laser = lasers[i];
        if (!laser || !laser.mesh || laser.mesh.isDisposed()) continue;

        for (let j = atheroscleroticPlaques.length - 1; j >= 0; j--) {
            const plaque = atheroscleroticPlaques[j];
            if (!plaque || !plaque.mesh || plaque.mesh.isDisposed()) continue;

            // Sprawdź czy blaszka jest zniszczalna
            if (!plaque.destructible) continue;

            // Prosta detekcja kolizji na podstawie odległości
            const distance = BABYLON.Vector3.Distance(
                laser.mesh.position,
                plaque.mesh.position
            );

            // Jeśli wykryto kolizję
            if (distance < plaque.size * 1.2) {
                // Zmniejsz punkty wytrzymałości blaszki
                plaque.currentHitPoints--;

                // Efekty trafienia
                if (typeof playSoundEffect === 'function') {
                    // Użyj dostępnego dźwięku (sprawdź, które zmienne są dostępne)
                    if (typeof destructionSound !== 'undefined' && destructionSound) {
                        playSoundEffect(destructionSound);
                    } else if (typeof laserSound !== 'undefined' && laserSound) {
                        playSoundEffect(laserSound);
                    } else {
                        // Fallback - odtwórz dźwięk bez określania bufora
                        playSoundEffect('laser');
                    }
                }

                if (typeof playParticleEffect === 'function') {
                    playParticleEffect("laserHit", plaque.mesh.position);
                }

                // Usuń laser
                if (typeof poolManager !== 'undefined' && poolManager.returnObject) {
                    laser.mesh.setEnabled(false);

                    poolManager.returnObject("laser", laser);
                } else {

                    laser.mesh.dispose();
                }
                lasers.splice(i, 1);

                // Sprawdź czy blaszka została zniszczona
                if (plaque.currentHitPoints <= 0) {
                    // Dodaj punkty
                    if (typeof score !== 'undefined') {
                        const pointsToAdd = 10 * plaque.ahaType; // Więcej punktów za trudniejsze blaszki
                        score += pointsToAdd;
                        console.log(`Zniszczono blaszkę miażdżycową typu AHA ${plaque.ahaType}. Dodano ${pointsToAdd} punktów.`);
                    }

                    // Efekty zniszczenia
                    if (typeof playParticleEffect === 'function') {
                        playParticleEffect("obstacleDestruction", plaque.mesh.position);
                    }

                    // Usuń blaszkę
                    plaque.mesh.dispose();
                    atheroscleroticPlaques.splice(j, 1);

                    console.log(`Zniszczono blaszkę miażdżycową typu AHA ${plaque.ahaType}`);
                } else {
                    // Efekt wizualny trafienia (np. zmiana koloru)
                    if (plaque.mesh.material) {
                        plaque.mesh.material.emissiveColor = new BABYLON.Color3(1, 0.3, 0.3); // Czerwony błysk

                        // Przywróć oryginalny kolor po chwili
                        setTimeout(() => {
                            if (plaque.mesh && !plaque.mesh.isDisposed() && plaque.mesh.material) {
                                const colorConfig = PLAQUE_CONFIG.COLORS[plaque.type] || {
                                    BASE: new BABYLON.Color3(0.7, 0.7, 0.7),
                                    EMISSION: new BABYLON.Color3(0.3, 0.3, 0.3),
                                    DAMAGE: 10
                                };
                                plaque.mesh.material.emissiveColor = colorConfig.EMISSION;
                            }
                        }, 200);
                    }
                }

                // Aktualizuj HUD
                if (typeof updateHUD === 'function') {
                    updateHUD();
                }

                break; // Laser trafił w coś, przejdź do następnego lasera
            }
        }
    }
}

/**
 * Przełącza widok przekroju poprzecznego zmian miażdżycowych
 * @param {boolean} enabled - Czy widok przekroju ma być włączony
 * @param {Object} vessel - Obiekt naczynia krwionośnego (opcjonalny)
 * @param {Object} innerVessel - Obiekt wewnętrznego naczynia krwionośnego (opcjonalny)
 */
function toggleCrossSectionView(enabled, vessel, innerVessel) {
    // Flaga stanu widoku przekroju
    window.crossSectionEnabled = enabled;

    // Aktualizuj przezroczystość zmian miażdżycowych
    for (let i = 0; i < atheroscleroticPlaques.length; i++) {
        const plaque = atheroscleroticPlaques[i];
        if (plaque.mesh && plaque.mesh.material) {
            if (enabled) {
                plaque.mesh.material.alpha = 0.8;
            } else {
                plaque.mesh.material.alpha = 1.0;
            }
        }
    }

    // Jeśli przekazano obiekty naczyń, zaktualizuj ich przezroczystość
    if (vessel && vessel.material) {
        vessel.material.alpha = enabled ? 0.1 : 0.3;
    }

    if (innerVessel && innerVessel.material) {
        innerVessel.material.alpha = enabled ? 0.05 : 0.2;
    }

    console.log(`Widok przekroju ${enabled ? 'włączony' : 'wyłączony'}`);
}

// Ulepszony detektor kolizji dla zmian miażdżycowych
function improvedPlaqueCollisionDetection(playerMesh, plaque) {
    if (!playerMesh) {
        return false;
    }

    // Dodatkowe sprawdzenie, czy playerMesh jest w ogóle meshem, który ma bounding box
    if (!playerMesh.getBoundingInfo || !playerMesh.getBoundingInfo().boundingSphere) {
        return false;
    }

    // Sprawdź, czy plaque.mesh istnieje
    if (!plaque.mesh) {
        return false;
    }

    // Sprawdź, czy plaque.mesh ma dzieci (jest TransformNode)
    if (plaque.mesh.getChildren) {
        const children = plaque.mesh.getChildren();

        // Jeśli nie ma dzieci, ale jest TransformNode, nie możemy sprawdzić kolizji
        if (children.length === 0) {
            return false;
        }

        // Sprawdź kolizje z każdym dzieckiem
        for (let child of children) {
            // Sprawdź, czy dziecko jest meshem i jest aktywne oraz nieusunięte
            if (child instanceof BABYLON.Mesh && child.isEnabled() && !child.isDisposed()) {
                // Użyj intersectsMesh z `true` dla precyzyjnej kolizji z geometrią dziecka
                // Sprawdź również, czy child ma bounding info, zanim go użyjesz
                if (child.getBoundingInfo && playerMesh.intersectsMesh(child, true)) {
                    return true;
                }
            }
        }
    } else if (plaque.mesh instanceof BABYLON.Mesh) {
        // Jeśli plaque.mesh jest bezpośrednio meshem, sprawdź kolizję z nim
        if (plaque.mesh.getBoundingInfo && playerMesh.intersectsMesh(plaque.mesh, true)) {
            return true;
        }
    }

    return false;
}

// Eksportuj funkcje do globalnego obiektu window
window.PLAQUE_TYPES = PLAQUE_TYPES;
window.generateAtheroscleroticPlaques = generateAtheroscleroticPlaques;
window.updateAtheroscleroticPlaques = updateAtheroscleroticPlaques;
window.checkPlaqueCollisions = checkPlaqueCollisions;
window.clearAtheroscleroticPlaques = clearAtheroscleroticPlaques;
window.checkLaserPlaqueCollisions = checkLaserPlaqueCollisions;
window.toggleCrossSectionView = toggleCrossSectionView;
window.toggleAnimation = toggleAnimation;

// Dummy funkcja createHoneycombTexture (nieużywana, ale eksportowana dla kompatybilności)
function createHoneycombTexture() {
    // Funkcja nie jest już używana, zwraca null
    return null;
}
window.createHoneycombTexture = createHoneycombTexture;
