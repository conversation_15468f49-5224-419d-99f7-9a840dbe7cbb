# 3D Bunny Tunnel - Interactive Medical Simulation

## Project Description

Bunny Tunnel is an interactive 3D medical simulation that allows users to navigate through a microscopic world of blood vessels. The application combines educational elements with engaging gameplay to create an immersive experience for learning about blood flow, arterial structures, and cardiovascular health.

## Key Features

- **3D Navigation**: Explore a realistic 3D environment representing blood vessels using Babylon.js
- **Educational Content**: Learn about blood flow, atherosclerotic plaques, and cardiovascular health
- **Level Progression**: Multiple levels with increasing difficulty and complexity
- **Multiplayer Support**: Join or host multiplayer sessions to explore with others
- **Mobile Compatibility**: Responsive design with touch controls for mobile devices
- **Performance Optimization**: Adaptive graphics settings for different devices
- **Educational Mode**: Optional learning mode with educational questions during gameplay

## Recent Updates

- Added new textures for improved visual fidelity
- Implemented optimized blood cell system for better performance
- Enhanced multiplayer functionality
- Added atherosclerotic plaques simulation
- Improved mobile controls and responsiveness
- Added educational pause feature with learning content

## Technical Details

- Built with Babylon.js for 3D rendering
- Multiplayer functionality using Azure Web PubSub
- Responsive design for both desktop and mobile devices
- Optimized performance with adaptive graphics settings

## Important: How to Run the Application

### CORS Issue Solution

If you're seeing an error like this:
```
Access to image at 'file:///Users/<USER>/3DBunny/textures/Marble_Red_004_basecolor.jpg' from origin 'null' has been blocked by CORS policy
```

This is because you're trying to open the HTML file directly from your filesystem. Due to browser security restrictions (CORS policy), loading local resources like textures won't work when opening HTML files directly.

### Quick Start

1. Use the included launcher:
   - Open `launcher.html` in your browser
   - Follow the instructions to start the server and launch the application

2. Or start the server manually:
   ```bash
   # Make the script executable (only needed once)
   chmod +x serve.sh

   # Run the server
   ./serve.sh
   ```

3. Open your browser and go to: http://localhost:8000

### Detailed Instructions

For more detailed instructions, see the [HOW_TO_RUN.md](HOW_TO_RUN.md) file.

## Project Structure

- `index.html` - Main application file
- `main.js` - Core initialization and game loop
- `gameLogic.js` - Main game mechanics and rules
- `bloodCells.js` - Blood cell simulation and behavior
- `player.js` - Player character controls and properties
- `multiplayer.js` - Multiplayer functionality
- `atheroscleroticPlaques.js` - Simulation of arterial plaques
- `ffrCalculations.js` - Fractional Flow Reserve calculations
- `effects.js` - Visual effects and particles
- `ui.js` - User interface elements and interactions
- `server.js` - Node.js server for serving the application
- `serve.sh` - Shell script to start the server
- `launcher.html` - User-friendly launcher for the application
- `textures/` - Directory containing texture files
- `HOW_TO_RUN.md` - Detailed instructions for running the application

## Contributing

Contributions to improve the simulation or add new educational features are welcome. Please ensure all changes maintain compatibility with both desktop and mobile devices.
