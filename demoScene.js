// demoScene.js
// Moduł do obsługi tła demonstracyjnego w menu głównym gry

let demoObjects = [];
let demoCamera = null;
let demoActive = false;
let demoTime = 0;

// --- EDUKACYJNA PREZENTACJA W DEMO ---
let demoPresentationIndex = 0;
const demoPresentations = [
    {
        type: 'artery_overview',
        description: 'To jest główne naczynie wieńcowe. Przepływa przez nie krew bogata w tlen.',
        focusPosition: { x: 0, y: 0, z: 0 }
    },
    {
        type: 'plaque',
        description: 'Zmiana miażdżycowa: zwężenie światła naczynia przez blaszkę lipidową.',
        focusPosition: { x: 2, y: 0, z: 10 }
    },
    {
        type: 'thrombus',
        description: 'Zakrzep: nagłe zamknięcie naczynia przez skrzep krwi.',
        focusPosition: { x: -1, y: 0, z: 20 }
    },
    {
        type: 'blood_cell',
        description: 'Czerwone krwinki transportują tlen do tkanek.',
        focusPosition: { x: 1, y: 0, z: 5 }
    }
];
let demoPresentationTimer = null;

/**
 * Ustawia kamerę i podświetlenie na wybranym elemencie demo/prezentacji edukacyjnej
 * @param {object} scene - scena gry
 * @param {object} pres - obiekt prezentacji (z demoPresentations)
 */
function highlightDemoElement(scene, pres) {
    if (!scene || !pres) return;
    // Ustaw kamerę na dany punkt
    if (scene.activeCamera && pres.focusPosition) {
        scene.activeCamera.setTarget(new BABYLON.Vector3(pres.focusPosition.x, pres.focusPosition.y, pres.focusPosition.z));
        scene.activeCamera.position = new BABYLON.Vector3(pres.focusPosition.x, pres.focusPosition.y + 3, pres.focusPosition.z - 8);
    }
    // Uproszczone ustawianie światła
    if (scene.lights && scene.lights.length > 0) {
        let color;
        if (pres.type === 'plaque' || pres.type === 'thrombus') {
            color = new BABYLON.Color3(1, 0.5, 0.2); // pomarańczowy
        } else if (pres.type === 'artery_overview') {
            color = new BABYLON.Color3(0.7, 0.7, 1); // niebieskawy
        } else {
            color = new BABYLON.Color3(1, 1, 1); // domyślne światło
        }
        scene.lights[0].diffuse = color;
    }
}

function presentDemoHighlight(scene) {
    if (!demoActive || !scene) return;
    const pres = demoPresentations[demoPresentationIndex % demoPresentations.length];
    highlightDemoElement(scene, pres);
    // Wyświetl overlay edukacyjny
    if (typeof window.showEducationalOverlay === 'function') {
        window.showEducationalOverlay(pres.description);
    }
    // Po 3 sekundach ukryj overlay i przejdź do kolejnego elementu
    clearTimeout(demoPresentationTimer);
    demoPresentationTimer = setTimeout(() => {
        if (typeof window.hideEducationalOverlay === 'function') {
            window.hideEducationalOverlay();
        }
        demoPresentationIndex++;
        // Zaplanuj kolejną prezentację
        presentDemoHighlight(scene);
    }, 3000);
}

// Inicjalizacja systemu emiterów krwinek dla trybu demo
function initializeDemoBloodEmitterSystem(scene) {
    if (!scene) return;

    // Usuń istniejące obiekty demo
    for (let obj of demoObjects) {
        if (obj && obj.dispose) {
            obj.dispose();
        }
    }
    demoObjects = [];

    // Create basic visual elements for demo scene
    try {
        // Create a simple tunnel for the demo scene
        const tunnel = BABYLON.MeshBuilder.CreateTube("demoTunnel", {
            path: [
                new BABYLON.Vector3(0, 0, -30),
                new BABYLON.Vector3(0, 0, 30)
            ],
            radius: 5,
            tessellation: 32,
            updatable: false
        }, scene);

        // Add material to the tunnel
        const tunnelMaterial = new BABYLON.StandardMaterial("demoTunnelMaterial", scene);
        tunnelMaterial.diffuseColor = new BABYLON.Color3(0.7, 0.1, 0.1); // Red color for blood vessel
        tunnelMaterial.alpha = 0.8; // Slightly transparent
        tunnel.material = tunnelMaterial;

        // Invert the normals to see the inside of the tunnel
        tunnel.flipFaces();

        // Add to demo objects for cleanup
        demoObjects.push(tunnel);

        // Initialize optimized blood system for demo
        if (typeof initializeBiologicalManager === 'function') {
            try {
                initializeBiologicalManager(scene);
                console.log("Demo mode: BiologicalManager initialized for demo scene");
            } catch (error) {
                console.error("Error initializing BiologicalManager in demo mode:", error);
                createFallbackBloodCells(scene);
            }
        } else if (typeof createOptimizedBloodCells === 'function') {
            try {
                // Create tunnel sections for blood cell system
                window.tunnelSections = [{
                    index: 0,
                    startPoint: new BABYLON.Vector3(0, 0, -30),
                    endPoint: new BABYLON.Vector3(0, 0, 30),
                    centerPoint: new BABYLON.Vector3(0, 0, 0),
                    radius: 5
                }];

                // Create bunny collider for blood cell system
                if (!window.bunnyCollider) {
                    window.bunnyCollider = new BABYLON.Mesh("dummyBunnyCollider", scene);
                    window.bunnyCollider.position = new BABYLON.Vector3(0, 0, 0);
                }

                createOptimizedBloodCells();
                console.log("Demo mode: Created optimized blood cells");
            } catch (error) {
                console.error("Error creating optimized blood cells in demo mode:", error);
                createFallbackBloodCells(scene);
            }
        } else {
            console.warn("Optimized blood cell system not available, using fallback");
            createFallbackBloodCells(scene);
        }

        if (DEBUG_MODE) {
            console.log("Demo mode: Created basic visual elements for demo scene");
        }
    } catch (error) {
        console.error("Error creating demo scene visual elements:", error);
    }
}

// Fallback function to create simple blood cells if optimized system fails
function createFallbackBloodCells(scene) {
    console.log("Demo mode: Using fallback blood cell creation");

    // Add some simple blood cell objects
    for (let i = 0; i < 20; i++) {
        const cell = BABYLON.MeshBuilder.CreateSphere("demoCell" + i, {
            diameter: 0.5 + Math.random() * 0.5
        }, scene);

        // Position randomly in the tunnel
        const angle = Math.random() * Math.PI * 2;
        const radius = 2 + Math.random() * 2;
        cell.position.x = Math.cos(angle) * radius;
        cell.position.y = Math.sin(angle) * radius;
        cell.position.z = -20 + Math.random() * 40;

        // Add material
        const cellMaterial = new BABYLON.StandardMaterial("demoCellMaterial" + i, scene);
        cellMaterial.diffuseColor = new BABYLON.Color3(0.9, 0.2, 0.2); // Red for blood cells
        cell.material = cellMaterial;

        // Add to demo objects for cleanup
        demoObjects.push(cell);
    }
}

// Funkcja sprawdzająca czy demo jest aktywne - dostępna globalnie
window.isDemoActive = function() {
    return demoActive;
};

// Funkcja inicjalizująca tryb demo - dostępna globalnie
window.initDemoScene = function(scene) {
    demoActive = true;
    demoPresentationIndex = 0;
    demoTime = 0;

    // Create a camera for the demo scene if it doesn't exist
    if (!scene.activeCamera) {
        // Create a free camera for the demo scene
        demoCamera = new BABYLON.FreeCamera("demoCamera", new BABYLON.Vector3(0, 0.5, -20), scene);
        demoCamera.setTarget(new BABYLON.Vector3(0, 0, 0));

        // Set the demo camera as the active camera for the scene
        scene.activeCamera = demoCamera;
        console.log("Demo camera created and set as active camera");
    } else {
        // Store reference to existing camera
        demoCamera = scene.activeCamera;
    }

    // Inicjalizuj system emiterów krwinek
    initializeDemoBloodEmitterSystem(scene);

    // Rozpocznij prezentację
    presentDemoHighlight(scene);
    console.log('Demo scene initialized');
};

// Funkcja aktualizująca tryb demo - dostępna globalnie
window.updateDemoScene = function(delta) {
    if (!demoActive) return;

    demoTime += delta;

    // Pobierz referencję do aktualnej sceny
    const currentScene = window.scene || (typeof BABYLON !== 'undefined' && BABYLON.Engine.LastCreatedScene);

    if (!currentScene) return;

    // Aktualizuj systemy krwinek w trybie demo
    if (typeof updateBiologicalManager === 'function') {
        // Aktualizuj biologiczny manager jeśli jest dostępny
        updateBiologicalManager(delta);
    } else if (typeof updateOptimizedBloodCells === 'function') {
        // Aktualizuj zoptymalizowane krwinki jeśli są dostępne
        updateOptimizedBloodCells(delta);
    } else if (typeof updateRedBloodCells === 'function') {
        // Fallback do standardowej aktualizacji krwinek
        updateRedBloodCells(delta);
    }

    // Aktualizuj pozycję bunny collider dla systemu krwinek
    if (window.bunnyCollider) {
        // Przesuń bunny collider wraz z kamerą, aby krwinki były generowane w odpowiednim miejscu
        if (currentScene.activeCamera) {
            window.bunnyCollider.position.z = currentScene.activeCamera.position.z - 10;
        }
    }

    // Powolny ruch kamery przez tunel
    if (currentScene && currentScene.activeCamera) {
        // Tylko jeśli nie jest w trakcie prezentacji konkretnego elementu
        if (!demoPresentationTimer) {
            const camera = currentScene.activeCamera;

            // Zmodyfikowany ruch kamery, aby zapewnić pełny przegląd naczynia
            // Wolniejszy ruch do przodu dla lepszego widoku
            camera.position.z += 0.3 * delta;

            // Zwiększony zakres ruchu kamery, aby pokryć całe naczynie
            if (camera.position.z > 35) {
                camera.position.z = -35;

                // Powiadom użytkownika o pełnym przeglądzie naczynia
                if (typeof window.showEducationalOverlay === 'function') {
                    window.showEducationalOverlay("Pełny przegląd naczynia wieńcowego");
                    // Ukryj komunikat po 2 sekundach
                    setTimeout(() => {
                        if (typeof window.hideEducationalOverlay === 'function') {
                            window.hideEducationalOverlay();
                        }
                    }, 2000);
                }
            }

            // Zwiększone kołysanie kamery dla lepszego widoku ścian naczynia
            const swayAmount = 0.4; // Zwiększone z 0.2 do 0.4
            camera.position.x = Math.sin(demoTime * 0.3) * swayAmount;
            camera.position.y = Math.cos(demoTime * 0.2) * swayAmount + 0.5; // Lekko powyżej środka

            // Dodaj okresowe spojrzenie na ściany naczynia
            const lookCycle = Math.sin(demoTime * 0.1);
            if (lookCycle > 0.9) { // Okresowo spójrz na ścianę
                const lookAngle = Math.sin(demoTime * 0.2) * 0.5;
                camera.setTarget(new BABYLON.Vector3(
                    Math.sin(demoTime * 0.3) * 3,
                    Math.cos(demoTime * 0.2) * 3,
                    camera.position.z - 5
                ));
            } else {
                // Normalnie patrz do przodu
                camera.setTarget(new BABYLON.Vector3(0, 0, camera.position.z - 10));
            }
        }
    }
};

// Funkcja czyszcząca tryb demo - dostępna globalnie
window.cleanupDemoScene = function(scene) {
    demoActive = false;
    clearTimeout(demoPresentationTimer);

    // Usuń obiekty demo
    for (let obj of demoObjects) {
        if (obj && obj.dispose) {
            obj.dispose();
        }
    }
    demoObjects = [];

    // Wyczyść zoptymalizowany system biologiczny
    if (typeof cleanupBiologicalManager === 'function') {
        try {
            cleanupBiologicalManager();
            console.log("Demo mode: BiologicalManager cleaned up");
        } catch (error) {
            console.error("Error cleaning up BiologicalManager:", error);
        }
    }

    // Wyczyść zoptymalizowany system krwinek
    if (typeof clearOptimizedBloodCells === 'function') {
        try {
            clearOptimizedBloodCells();
            console.log("Demo mode: Optimized blood cells cleaned up");
        } catch (error) {
            console.error("Error cleaning up optimized blood cells:", error);
        }
    }

    // Wyczyść standardowy system emiterów krwinek
    if (typeof clearBloodCells === 'function') {
        try {
            clearBloodCells();
            console.log("Demo mode: Standard blood cells cleaned up");
        } catch (error) {
            console.error("Error cleaning up standard blood cells:", error);
        }
    }

    // Wyczyść stary system emiterów krwinek
    if (typeof bloodEmitterSystem !== 'undefined' && bloodEmitterSystem && bloodEmitterSystem.cleanup) {
        try {
            bloodEmitterSystem.cleanup();
            console.log("Demo mode: Blood emitter system cleaned up");
        } catch (error) {
            console.error("Error cleaning up blood emitter system:", error);
        }
    }

    // Usuń referencje do tunelu i bunny collidera
    if (window.tunnelSections) {
        window.tunnelSections = [];
    }

    if (window.bunnyCollider) {
        if (window.bunnyCollider.dispose) {
            window.bunnyCollider.dispose();
        }
        window.bunnyCollider = null;
    }

    if (typeof window.hideEducationalOverlay === 'function') {
        window.hideEducationalOverlay();
    }
    console.log('Demo scene cleaned up');
};

// Funkcja sprawdzająca czy tryb demo jest aktywny - dostępna globalnie
window.isDemoActive = function() {
    return demoActive;
};
